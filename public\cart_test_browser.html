<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Test - Browser Session</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            background: #fafafa;
        }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            cursor: pointer; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 4px; 
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 4px; 
            font-family: monospace;
            font-size: 12px;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h3 {
            color: #555;
            margin-top: 0;
        }
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Cart Functionality Test</h1>
        <p style="text-align: center; color: #666;">This test verifies that cart operations work correctly and persist in browser sessions.</p>
        
        <div class="test-section">
            <h3><span class="step-number">1</span>Check Initial Status</h3>
            <button onclick="checkStatus()">Check Cart Status</button>
            <div id="status-result" class="result info">Click "Check Cart Status" to see current session info</div>
        </div>
        
        <div class="test-section">
            <h3><span class="step-number">2</span>Add Item to Cart</h3>
            <button onclick="addToCart()">Add Wireless Headphones (Qty: 3)</button>
            <div id="add-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3><span class="step-number">3</span>Update Item Quantity</h3>
            <button onclick="updateCart()">Update to Quantity: 7</button>
            <div id="update-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3><span class="step-number">4</span>View Cart Page</h3>
            <button onclick="window.open('/asma/public/cart', '_blank')">Open Cart Page in New Tab</button>
            <p style="margin: 10px 0; color: #666; font-size: 14px;">
                ✓ Check if the cart shows the correct items and quantities<br>
                ✓ Try updating quantities on the cart page<br>
                ✓ Verify changes persist after page refresh
            </p>
        </div>
        
        <div class="test-section">
            <h3><span class="step-number">5</span>Test Persistence</h3>
            <button onclick="location.reload()">Refresh This Page</button>
            <p style="margin: 10px 0; color: #666; font-size: 14px;">
                After refresh, click "Check Cart Status" to verify items are still there
            </p>
        </div>
        
        <div class="test-section">
            <h3><span class="step-number">6</span>Remove Item</h3>
            <button onclick="removeFromCart()">Remove Item</button>
            <div id="remove-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3><span class="step-number">7</span>Clear Cart</h3>
            <button onclick="clearCart()">Clear Entire Cart</button>
            <div id="clear-result" class="result"></div>
        </div>
    </div>

    <script>
        async function checkStatus() {
            try {
                const response = await fetch('/asma/public/cart_diagnostic.php?action=status');
                const data = await response.json();
                const resultDiv = document.getElementById('status-result');
                
                resultDiv.className = 'result info';
                resultDiv.innerHTML = `
                    <strong>Session ID:</strong> ${data.session_id}<br>
                    <strong>Session Cart:</strong> ${JSON.stringify(data.session_cart)}<br>
                    <strong>Product Available:</strong> ${data.product_exists ? '✓ Yes' : '✗ No'}<br>
                    <strong>User Status:</strong> ${data.user_logged_in ? 'Logged In' : 'Guest'}<br>
                    <strong>PHP Version:</strong> ${data.php_version}
                `;
            } catch (error) {
                document.getElementById('status-result').innerHTML = `✗ Error: ${error.message}`;
                document.getElementById('status-result').className = 'result error';
            }
        }
        
        async function addToCart() {
            try {
                const response = await fetch('/asma/public/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=1&quantity=3'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('add-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✓ ${data.message}<br>Cart Count: ${data.cartCount || 'N/A'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `✗ ${data.message}`;
                }
            } catch (error) {
                document.getElementById('add-result').innerHTML = `✗ Error: ${error.message}`;
                document.getElementById('add-result').className = 'result error';
            }
        }
        
        async function updateCart() {
            try {
                const response = await fetch('/asma/public/cart/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=1&quantity=7'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('update-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✓ ${data.message}<br>Cart Count: ${data.cartCount}<br>Cart Total: $${data.cartTotal}<br>Item Total: $${data.itemTotal}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `✗ ${data.message}`;
                }
            } catch (error) {
                document.getElementById('update-result').innerHTML = `✗ Error: ${error.message}`;
                document.getElementById('update-result').className = 'result error';
            }
        }
        
        async function removeFromCart() {
            try {
                const response = await fetch('/asma/public/cart/remove', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=1'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('remove-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✓ ${data.message}<br>Cart Count: ${data.cartCount}<br>Cart Total: $${data.cartTotal}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `✗ ${data.message}`;
                }
            } catch (error) {
                document.getElementById('remove-result').innerHTML = `✗ Error: ${error.message}`;
                document.getElementById('remove-result').className = 'result error';
            }
        }
        
        async function clearCart() {
            try {
                const response = await fetch('/asma/public/cart/clear', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('clear-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✓ ${data.message}<br>Cart Count: ${data.cartCount}<br>Cart Total: $${data.cartTotal}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `✗ ${data.message}`;
                }
            } catch (error) {
                document.getElementById('clear-result').innerHTML = `✗ Error: ${error.message}`;
                document.getElementById('clear-result').className = 'result error';
            }
        }
        
        // Auto-check status on page load
        window.addEventListener('load', checkStatus);
    </script>
</body>
</html>
