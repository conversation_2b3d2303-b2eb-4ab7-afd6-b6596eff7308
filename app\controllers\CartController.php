<?php

class CartController extends BaseController
{

    public function index()
    {
        $subtotal = $this->calculateCartTotal();
        $discountAmount = 0;
        $discountCode = null;

        // Check if discount code is applied
        if (isset($_SESSION['discount_code'])) {
            $stmt = $this->pdo->prepare("SELECT * FROM discount_codes WHERE code = ? AND is_active = 1");
            $stmt->execute([$_SESSION['discount_code']]);
            $discountCodeData = $stmt->fetch();

            if ($discountCodeData && $subtotal >= $discountCodeData['minimum_order_amount']) {
                $discountCode = $discountCodeData;

                if ($discountCodeData['discount_type'] === 'percentage') {
                    $discountAmount = $subtotal * ($discountCodeData['discount_value'] / 100);
                    if ($discountCodeData['maximum_discount']) {
                        $discountAmount = min($discountAmount, $discountCodeData['maximum_discount']);
                    }
                } else {
                    $discountAmount = $discountCodeData['discount_value'];
                }
            } else {
                // Remove invalid discount code from session
                unset($_SESSION['discount_code']);
            }
        }

        $total = $subtotal - $discountAmount;

        $this->render('cart/index', [
            'cart' => $this->cart,
            'subtotal' => $subtotal,
            'discountCode' => $discountCode,
            'discountAmount' => $discountAmount,
            'total' => $total
        ]);
    }

    public function add()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->json(['success' => false, 'message' => 'Invalid request method.']);
                return;
            }
            $productId = (int)($_POST['product_id'] ?? 0);
            $quantity = max(1, (int)($_POST['quantity'] ?? 1));
            if (!$productId) {
                $this->json(['success' => false, 'message' => 'Invalid product']);
                return;
            }
            // Check if product exists and is active
            $stmt = $this->pdo->prepare("SELECT * FROM products WHERE id = ? AND is_active = 1");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
            if (!$product) {
                $this->json(['success' => false, 'message' => 'Product not found']);
                return;
            }
            // Check stock
            if ($product['stock_quantity'] < $quantity) {
                $this->json(['success' => false, 'message' => 'Not enough stock available']);
                return;
            }
            if ($this->user) {
                require_once APP_ROOT . '/app/models/Cart.php';
                $cartModel = new Cart($this->pdo);
                $cartModel->addItem($this->user['id'], $productId, $quantity);
            } else {
                if (!isset($_SESSION['cart'])) {
                    $_SESSION['cart'] = [];
                }
                if (isset($_SESSION['cart'][$productId])) {
                    $_SESSION['cart'][$productId] += $quantity;
                } else {
                    $_SESSION['cart'][$productId] = $quantity;
                }
            }

            // Refresh the cart data after adding
            $this->cart = $this->getCart();
            $cartCount = count($this->cart); // Number of unique items, not total quantity

            $this->json([
                'success' => true,
                'message' => 'Product added to cart',
                'cartCount' => $cartCount
            ]);
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }

    public function update()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->json(['success' => false, 'message' => 'Invalid request method.']);
                return;
            }
            $productId = (int)($_POST['product_id'] ?? 0);
            $quantity = max(0, (int)($_POST['quantity'] ?? 0));
            if (!$productId) {
                $this->json(['success' => false, 'message' => 'Invalid product']);
                return;
            }
            if ($this->user) {
                require_once APP_ROOT . '/app/models/Cart.php';
                $cartModel = new Cart($this->pdo);
                $cartModel->updateQuantity($this->user['id'], $productId, $quantity);
            } else {
                if (!isset($_SESSION['cart'])) {
                    $_SESSION['cart'] = [];
                }
                if ($quantity > 0) {
                    $_SESSION['cart'][$productId] = $quantity;
                } else {
                    unset($_SESSION['cart'][$productId]);
                }
            }
            $this->cart = $this->getCart();
            $cart = $this->cart;
            $cartCount = count($cart); // Number of unique items, not total quantity
            $subtotal = 0;
            $itemTotal = 0;
            foreach ($cart as $item) {
                $subtotal += ($item['sale_price'] ?? $item['price']) * $item['quantity'];
                $currentProductId = $item['product_id'] ?? $item['id'];
                if ($currentProductId == $productId) {
                    $itemTotal = ($item['sale_price'] ?? $item['price']) * $item['quantity'];
                }
            }

            // Calculate discount if applied
            $discountAmount = 0;
            if (isset($_SESSION['discount_code'])) {
                $stmt = $this->pdo->prepare("SELECT * FROM discount_codes WHERE code = ? AND is_active = 1");
                $stmt->execute([$_SESSION['discount_code']]);
                $discountCode = $stmt->fetch();

                if ($discountCode && $subtotal >= $discountCode['minimum_order_amount']) {
                    if ($discountCode['discount_type'] === 'percentage') {
                        $discountAmount = $subtotal * ($discountCode['discount_value'] / 100);
                        if ($discountCode['maximum_discount']) {
                            $discountAmount = min($discountAmount, $discountCode['maximum_discount']);
                        }
                    } else {
                        $discountAmount = $discountCode['discount_value'];
                    }
                }
            }

            $finalTotal = $subtotal - $discountAmount;

            $this->json([
                'success' => true,
                'message' => 'Cart updated',
                'cartCount' => $cartCount,
                'cartTotal' => number_format($finalTotal, 2),
                'itemTotal' => number_format($itemTotal, 2),
                'discountAmount' => number_format($discountAmount, 2)
            ]);
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }



    public function clear()
    {
        try {
            if ($this->user) {
                require_once APP_ROOT . '/app/models/Cart.php';
                $cartModel = new Cart($this->pdo);
                $cartModel->clearCart($this->user['id']);
            } else {
                $_SESSION['cart'] = [];
            }
            $this->json([
                'success' => true,
                'message' => 'Cart cleared',
                'cartCount' => 0,
                'cartTotal' => number_format(0, 2)
            ]);
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }

    public function checkout()
    {
        if (empty($this->cart)) {
            $this->redirect('cart');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->processCheckout();
            return;
        }

        // Get discount code if applied
        $discountCode = null;
        $discountAmount = 0;
        if (isset($_SESSION['discount_code'])) {
            $stmt = $this->pdo->prepare("SELECT * FROM discount_codes WHERE code = ? AND is_active = 1");
            $stmt->execute([$_SESSION['discount_code']]);
            $discountCode = $stmt->fetch();

            if ($discountCode) {
                $subtotal = $this->calculateCartTotal();
                if ($subtotal >= $discountCode['minimum_order_amount']) {
                    if ($discountCode['discount_type'] === 'percentage') {
                        $discountAmount = $subtotal * ($discountCode['discount_value'] / 100);
                        if ($discountCode['maximum_discount']) {
                            $discountAmount = min($discountAmount, $discountCode['maximum_discount']);
                        }
                    } else {
                        $discountAmount = $discountCode['discount_value'];
                    }
                }
            }
        }

        $subtotal = $this->calculateCartTotal();
        $shippingCost = $this->calculateShippingCost($subtotal - $discountAmount);
        $taxRate = $this->getTaxRate();
        $taxAmount = ($subtotal - $discountAmount) * ($taxRate / 100);
        $total = $subtotal - $discountAmount + $shippingCost + $taxAmount;

        $this->render('cart/checkout', [
            'cart' => $this->cart,
            'subtotal' => $subtotal,
            'discountCode' => $discountCode,
            'discountAmount' => $discountAmount,
            'shippingCost' => $shippingCost,
            'taxRate' => $taxRate,
            'taxAmount' => $taxAmount,
            'total' => $total
        ]);
    }

    private function processCheckout()
    {
        $firstName = $this->sanitizeInput($_POST['first_name'] ?? '');
        $lastName = $this->sanitizeInput($_POST['last_name'] ?? '');
        $email = $this->sanitizeInput($_POST['email'] ?? '');
        $phone = $this->sanitizeInput($_POST['phone'] ?? '');
        $address = $this->sanitizeInput($_POST['address'] ?? '');
        $city = $this->sanitizeInput($_POST['city'] ?? '');
        $state = $this->sanitizeInput($_POST['state'] ?? '');
        $zipCode = $this->sanitizeInput($_POST['zip_code'] ?? '');
        $country = $this->sanitizeInput($_POST['country'] ?? 'United States');
        $paymentMethod = $this->sanitizeInput($_POST['payment_method'] ?? '');
        $notes = $this->sanitizeInput($_POST['notes'] ?? '');

        // Validation
        if (empty($firstName) || empty($lastName) || empty($email) || empty($address) || empty($city) || empty($state) || empty($zipCode)) {
            $error = 'Please fill in all required fields.';
        } elseif (!$this->validateEmail($email)) {
            $error = 'Please enter a valid email address.';
        } else {
            try {
                $this->pdo->beginTransaction();

                // Calculate totals
                $subtotal = $this->calculateCartTotal();
                $discountAmount = 0;
                $discountCodeId = null;

                if (isset($_SESSION['discount_code'])) {
                    $stmt = $this->pdo->prepare("SELECT * FROM discount_codes WHERE code = ? AND is_active = 1");
                    $stmt->execute([$_SESSION['discount_code']]);
                    $discountCode = $stmt->fetch();

                    if ($discountCode && $subtotal >= $discountCode['minimum_order_amount']) {
                        $discountCodeId = $discountCode['id'];
                        if ($discountCode['discount_type'] === 'percentage') {
                            $discountAmount = $subtotal * ($discountCode['discount_value'] / 100);
                            if ($discountCode['maximum_discount']) {
                                $discountAmount = min($discountAmount, $discountCode['maximum_discount']);
                            }
                        } else {
                            $discountAmount = $discountCode['discount_value'];
                        }
                    }
                }

                $shippingCost = $this->calculateShippingCost($subtotal - $discountAmount);
                $taxRate = $this->getTaxRate();
                $taxAmount = ($subtotal - $discountAmount) * ($taxRate / 100);
                $total = $subtotal - $discountAmount + $shippingCost + $taxAmount;

                // Create order
                $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(uniqid());
                $shippingAddress = "$address, $city, $state $zipCode, $country";

                $stmt = $this->pdo->prepare("
                    INSERT INTO orders (order_number, user_id, guest_email, subtotal, tax_amount, shipping_amount, discount_amount, total_amount, payment_method, shipping_address, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $orderNumber,
                    $this->user ? $this->user['id'] : null,
                    $this->user ? null : $email,
                    $subtotal,
                    $taxAmount,
                    $shippingCost,
                    $discountAmount,
                    $total,
                    $paymentMethod,
                    $shippingAddress,
                    $notes
                ]);

                $orderId = $this->pdo->lastInsertId();

                // Add order items
                foreach ($this->cart as $item) {
                    $price = $item['sale_price'] ?: $item['price'];
                    $stmt = $this->pdo->prepare("
                        INSERT INTO order_items (order_id, product_id, product_name, product_sku, quantity, unit_price, total_price) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $orderId,
                        $item['id'],
                        $item['name'],
                        $item['sku'] ?? null,
                        $item['quantity'],
                        $price,
                        $price * $item['quantity']
                    ]);

                    // Update stock
                    $stmt = $this->pdo->prepare("UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?");
                    $stmt->execute([$item['quantity'], $item['id']]);
                }

                // Record discount code usage
                if ($discountCodeId) {
                    $stmt = $this->pdo->prepare("
                        INSERT INTO discount_code_usage (discount_code_id, user_id, order_id, discount_amount) 
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([$discountCodeId, $this->user ? $this->user['id'] : null, $orderId, $discountAmount]);

                    // Update discount code usage count
                    $stmt = $this->pdo->prepare("UPDATE discount_codes SET used_count = used_count + 1 WHERE id = ?");
                    $stmt->execute([$discountCodeId]);
                }

                // Clear cart
                if ($this->user) {
                    $stmt = $this->pdo->prepare("DELETE FROM cart_items WHERE user_id = ?");
                    $stmt->execute([$this->user['id']]);
                } else {
                    unset($_SESSION['cart']);
                }

                // Clear discount code
                unset($_SESSION['discount_code']);

                $this->pdo->commit();

                // Send order confirmation email
                $customerName = $this->user ? $this->user['first_name'] . ' ' . $this->user['last_name'] : $firstName . ' ' . $lastName;
                $customerEmail = $this->user ? $this->user['email'] : $email;

                $emailBody = "Dear $customerName,\n\nThank you for your order!\n\nOrder Number: $orderNumber\nTotal: $" . number_format($total, 2) . "\n\nWe will notify you when your order ships.\n\nBest regards,\n" . APP_NAME . " Team";
                $this->sendEmail($customerEmail, "Order Confirmation - $orderNumber", $emailBody);

                $this->redirect("order/$orderId");
            } catch (Exception $e) {
                $this->pdo->rollBack();
                $error = 'An error occurred while processing your order. Please try again.';
            }
        }

        if (isset($error)) {
            $this->render('cart/checkout', [
                'error' => $error,
                'cart' => $this->cart,
                'formData' => $_POST
            ]);
        }
    }

    public function applyDiscount()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->json(['success' => false, 'message' => 'Invalid request method']);
                return;
            }

            $code = $this->sanitizeInput($_POST['discount_code'] ?? '');

            if (empty($code)) {
                $this->json(['success' => false, 'message' => 'Please enter a discount code']);
                return;
            }

            $stmt = $this->pdo->prepare("SELECT * FROM discount_codes WHERE code = ? AND is_active = 1");
            $stmt->execute([$code]);
            $discountCode = $stmt->fetch();

            if (!$discountCode) {
                $this->json(['success' => false, 'message' => 'Invalid discount code']);
                return;
            }

            $subtotal = $this->calculateCartTotal();
            if ($subtotal < $discountCode['minimum_order_amount']) {
                $this->json([
                    'success' => false,
                    'message' => 'Minimum order amount of $' . number_format($discountCode['minimum_order_amount'], 2) . ' not met'
                ]);
                return;
            }

            // Apply discount code
            $_SESSION['discount_code'] = $code;

            // Calculate discount amount
            $discountAmount = 0;
            if ($discountCode['discount_type'] === 'percentage') {
                $discountAmount = $subtotal * ($discountCode['discount_value'] / 100);
                if ($discountCode['maximum_discount']) {
                    $discountAmount = min($discountAmount, $discountCode['maximum_discount']);
                }
            } else {
                $discountAmount = $discountCode['discount_value'];
            }

            $newTotal = $subtotal - $discountAmount;

            $this->json([
                'success' => true,
                'message' => 'Discount code applied successfully!',
                'discountAmount' => number_format($discountAmount, 2),
                'cartTotal' => number_format($newTotal, 2)
            ]);
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }

    public function removeDiscount()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->json(['success' => false, 'message' => 'Invalid request method']);
                return;
            }

            // Remove discount code from session
            unset($_SESSION['discount_code']);

            // Calculate new total
            $subtotal = $this->calculateCartTotal();

            $this->json([
                'success' => true,
                'message' => 'Discount code removed',
                'cartTotal' => number_format($subtotal, 2)
            ]);
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }

    private function calculateShippingCost($subtotal)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'free_shipping_threshold'");
            $stmt->execute();
            $result = $stmt->fetch();
            $freeShippingThreshold = $result ? (float)$result['setting_value'] : 50.00; // Default $50

            if ($subtotal >= $freeShippingThreshold) {
                return 0;
            }

            $stmt = $this->pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'shipping_cost'");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result ? (float)$result['setting_value'] : 5.99; // Default $5.99
        } catch (PDOException $e) {
            error_log("Database error in calculateShippingCost: " . $e->getMessage());
            return 5.99; // Default shipping cost
        }
    }

    private function getTaxRate()
    {
        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'tax_rate'");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result ? (float)$result['setting_value'] : 8.25; // Default 8.25%
        } catch (PDOException $e) {
            error_log("Database error in getTaxRate: " . $e->getMessage());
            return 8.25; // Default tax rate
        }
    }

    // Alias methods for routes
    public function addItem()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $this->add();
            } else {
                $this->json(['success' => false, 'message' => 'Invalid request method.']);
            }
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }

    public function updateItem()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $this->update();
            } else {
                $this->json(['success' => false, 'message' => 'Invalid request method.']);
            }
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }

    public function removeItem()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->json(['success' => false, 'message' => 'Invalid request method.']);
                return;
            }
            $productId = (int)($_POST['product_id'] ?? 0);
            if (!$productId) {
                $this->json(['success' => false, 'message' => 'Invalid product ID.']);
                return;
            }
            if ($this->user) {
                require_once APP_ROOT . '/app/models/Cart.php';
                $cartModel = new Cart($this->pdo);
                $cartModel->removeItem($this->user['id'], $productId);
            } else {
                if (isset($_SESSION['cart'][$productId])) {
                    unset($_SESSION['cart'][$productId]);
                }
            }
            $this->cart = $this->getCart();
            $cart = $this->cart;
            $cartCount = count($cart); // Number of unique items, not total quantity
            $subtotal = 0;
            foreach ($cart as $item) {
                $subtotal += ($item['sale_price'] ?? $item['price']) * $item['quantity'];
            }

            // Calculate discount if applied
            $discountAmount = 0;
            if (isset($_SESSION['discount_code'])) {
                $stmt = $this->pdo->prepare("SELECT * FROM discount_codes WHERE code = ? AND is_active = 1");
                $stmt->execute([$_SESSION['discount_code']]);
                $discountCode = $stmt->fetch();

                if ($discountCode && $subtotal >= $discountCode['minimum_order_amount']) {
                    if ($discountCode['discount_type'] === 'percentage') {
                        $discountAmount = $subtotal * ($discountCode['discount_value'] / 100);
                        if ($discountCode['maximum_discount']) {
                            $discountAmount = min($discountAmount, $discountCode['maximum_discount']);
                        }
                    } else {
                        $discountAmount = $discountCode['discount_value'];
                    }
                } else {
                    // Remove discount if minimum order amount is no longer met
                    unset($_SESSION['discount_code']);
                }
            }

            $finalTotal = $subtotal - $discountAmount;

            $this->json([
                'success' => true,
                'message' => 'Item removed from cart',
                'cartCount' => $cartCount,
                'cartTotal' => number_format($finalTotal, 2),
                'discountAmount' => number_format($discountAmount, 2)
            ]);
        } catch (\Throwable $e) {
            $this->json(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
        }
    }

    public function clearCart()
    {
        $this->clear();
    }

    public function orderDetail($orderId)
    {
        $this->requireAuth();

        $stmt = $this->pdo->prepare("
            SELECT o.*, oi.*, p.name as product_name, pi.image_path as primary_image
            FROM orders o 
            LEFT JOIN order_items oi ON o.id = oi.order_id 
            LEFT JOIN products p ON oi.product_id = p.id 
            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
            WHERE o.id = ? AND o.user_id = ?
        ");
        $stmt->execute([$orderId, $this->user['id']]);
        $orderItems = $stmt->fetchAll();

        if (empty($orderItems)) {
            $this->redirect('orders');
        }

        $order = [
            'id' => $orderItems[0]['order_id'],
            'order_number' => $orderItems[0]['order_number'],
            'status' => $orderItems[0]['status'],
            'total_amount' => $orderItems[0]['total_amount'],
            'created_at' => $orderItems[0]['created_at'],
            'items' => $orderItems
        ];

        $this->render('orders/detail', [
            'order' => $order
        ]);
    }

    public function orderHistory()
    {
        $this->requireAuth();

        $stmt = $this->pdo->prepare("
            SELECT * FROM orders 
            WHERE user_id = ? 
            ORDER BY created_at DESC
        ");
        $stmt->execute([$this->user['id']]);
        $orders = $stmt->fetchAll();

        $this->render('orders/history', [
            'orders' => $orders
        ]);
    }

    public function cancelOrder($orderId)
    {
        $this->requireAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('orders');
            return;
        }

        $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE id = ? AND user_id = ? AND status IN ('pending', 'processing')");
        $stmt->execute([$orderId, $this->user['id']]);
        $order = $stmt->fetch();

        if (!$order) {
            $_SESSION['error'] = 'Order not found or cannot be cancelled.';
            $this->redirect('orders');
            return;
        }

        $this->pdo->beginTransaction();

        try {
            // Update order status
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'cancelled', updated_at = NOW() WHERE id = ?");
            $stmt->execute([$orderId]);

            // Restore product stock
            $stmt = $this->pdo->prepare("
                SELECT oi.product_id, oi.quantity 
                FROM order_items oi 
                WHERE oi.order_id = ?
            ");
            $stmt->execute([$orderId]);
            $orderItems = $stmt->fetchAll();

            foreach ($orderItems as $item) {
                $stmt = $this->pdo->prepare("UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?");
                $stmt->execute([$item['quantity'], $item['product_id']]);
            }

            $this->pdo->commit();

            // Send cancellation email
            $emailBody = "Dear {$this->user['first_name']},\n\nYour order #{$order['order_number']} has been cancelled successfully.\n\nIf you have any questions, please contact our support team.\n\nBest regards,\n" . APP_NAME . " Team";
            $this->sendEmail($this->user['email'], "Order Cancelled - {$order['order_number']}", $emailBody);

            $_SESSION['success'] = 'Order cancelled successfully.';
        } catch (Exception $e) {
            $this->pdo->rollBack();
            $_SESSION['error'] = 'Failed to cancel order. Please try again.';
        }

        $this->redirect('orders');
    }

    public function stripeWebhook()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            exit;
        }

        $payload = file_get_contents('php://input');
        $sigHeader = $_SERVER['HTTP_STRIPE_SIGNATURE'];
        $endpointSecret = 'whsec_your_stripe_webhook_secret'; // Replace with your webhook secret

        try {
            // Note: Stripe SDK needs to be installed for this to work
            // $event = \Stripe\Webhook::constructEvent($payload, $sigHeader, $endpointSecret);

            // For now, just log the webhook
            error_log("Stripe webhook received: " . $payload);
        } catch (Exception $e) {
            http_response_code(400);
            exit;
        }

        // Process the webhook data
        $data = json_decode($payload, true);
        $eventType = $data['type'] ?? '';

        switch ($eventType) {
            case 'payment_intent.succeeded':
                $this->handlePaymentSuccess($data['data']['object']);
                break;
            case 'payment_intent.payment_failed':
                $this->handlePaymentFailure($data['data']['object']);
                break;
            default:
                http_response_code(200);
                exit;
        }

        http_response_code(200);
    }

    public function paypalWebhook()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            exit;
        }

        $payload = file_get_contents('php://input');
        $headers = getallheaders();

        // Verify PayPal webhook signature
        if (!$this->verifyPayPalWebhook($payload, $headers)) {
            http_response_code(400);
            exit;
        }

        $data = json_decode($payload, true);
        $eventType = $data['event_type'] ?? '';

        switch ($eventType) {
            case 'PAYMENT.CAPTURE.COMPLETED':
                $this->handlePayPalPaymentSuccess($data);
                break;
            case 'PAYMENT.CAPTURE.DENIED':
                $this->handlePayPalPaymentFailure($data);
                break;
            default:
                http_response_code(200);
                exit;
        }

        http_response_code(200);
    }

    private function handlePaymentSuccess($paymentIntent)
    {
        $orderNumber = $paymentIntent['metadata']['order_number'] ?? null;

        if ($orderNumber) {
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'paid', payment_method = 'stripe', updated_at = NOW() WHERE order_number = ?");
            $stmt->execute([$orderNumber]);
        }
    }

    private function handlePaymentFailure($paymentIntent)
    {
        $orderNumber = $paymentIntent['metadata']['order_number'] ?? null;

        if ($orderNumber) {
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'payment_failed', updated_at = NOW() WHERE order_number = ?");
            $stmt->execute([$orderNumber]);
        }
    }

    private function handlePayPalPaymentSuccess($data)
    {
        $orderNumber = $data['resource']['custom_id'] ?? null;

        if ($orderNumber) {
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'paid', payment_method = 'paypal', updated_at = NOW() WHERE order_number = ?");
            $stmt->execute([$orderNumber]);
        }
    }

    private function handlePayPalPaymentFailure($data)
    {
        $orderNumber = $data['resource']['custom_id'] ?? null;

        if ($orderNumber) {
            $stmt = $this->pdo->prepare("UPDATE orders SET status = 'payment_failed', updated_at = NOW() WHERE order_number = ?");
            $stmt->execute([$orderNumber]);
        }
    }

    private function verifyPayPalWebhook($payload, $headers)
    {
        // Implement PayPal webhook verification
        // This is a simplified version - you should implement proper verification
        return true;
    }
}
