<?php
// Debug cart session data
session_start();

echo "<h2>Cart Debug Information</h2>";

echo "<h3>Session Status:</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Status: " . session_status() . "<br>";

echo "<h3>Raw Cart Data:</h3>";
echo "<pre>";
var_dump($_SESSION['cart'] ?? 'No cart data');
echo "</pre>";

echo "<h3>Cart Analysis:</h3>";
$cartItems = $_SESSION['cart'] ?? [];
echo "Total items in cart array: " . count($cartItems) . "<br>";

$validItemCount = 0;
$subtotal = 0;

foreach ($cartItems as $index => $item) {
    echo "<h4>Item $index:</h4>";
    echo "<pre>";
    var_dump($item);
    echo "</pre>";
    
    echo "Is array: " . (is_array($item) ? 'Yes' : 'No') . "<br>";
    
    if (is_array($item)) {
        echo "Has name: " . (isset($item['name']) ? 'Yes (' . $item['name'] . ')' : 'No') . "<br>";
        echo "Has price: " . (isset($item['price']) ? 'Yes (' . $item['price'] . ')' : 'No') . "<br>";
        echo "Has quantity: " . (isset($item['quantity']) ? 'Yes (' . $item['quantity'] . ')' : 'No') . "<br>";
        
        if (isset($item['quantity'])) {
            echo "Quantity > 0: " . ($item['quantity'] > 0 ? 'Yes' : 'No') . "<br>";
        }
        
        // Check if item is valid for checkout
        if (isset($item['name'], $item['price'], $item['quantity']) && $item['quantity'] > 0) {
            $validItemCount++;
            echo "<strong>✓ This item is VALID for checkout</strong><br>";
            
            if (isset($item['price']) && isset($item['quantity'])) {
                $itemTotal = (float)$item['price'] * (int)$item['quantity'];
                $subtotal += $itemTotal;
                echo "Item total: $" . number_format($itemTotal, 2) . "<br>";
            }
        } else {
            echo "<strong>✗ This item is INVALID for checkout</strong><br>";
        }
    }
    echo "<hr>";
}

echo "<h3>Summary:</h3>";
echo "Valid items for checkout: $validItemCount<br>";
echo "Subtotal: $" . number_format($subtotal, 2) . "<br>";

if ($validItemCount === 0) {
    echo "<strong style='color: red;'>⚠️ CHECKOUT WILL REDIRECT - No valid items!</strong><br>";
} else {
    echo "<strong style='color: green;'>✓ Checkout should proceed normally</strong><br>";
}

echo "<h3>Other Session Data:</h3>";
echo "User: ";
var_dump($_SESSION['user'] ?? 'Not logged in');
echo "<br>";

echo "Discount: ";
var_dump($_SESSION['discount'] ?? 'No discount');
echo "<br>";

echo "Discount Code: ";
var_dump($_SESSION['discount_code'] ?? 'No discount code');
echo "<br>";

echo "<h3>Actions:</h3>";
echo '<a href="/cart">Go to Cart</a> | ';
echo '<a href="/checkout">Go to Checkout</a> | ';
echo '<a href="debug_cart.php">Refresh Debug</a>';
?>
