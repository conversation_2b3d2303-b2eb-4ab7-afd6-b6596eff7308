<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get cart items and totals - use processed cart data from controller
$cartItems = [];
if (isset($cart) && is_array($cart)) {
    $cartItems = $cart;
    error_log('Checkout page - Using processed cart data from controller');
} else {
    $cartItems = $_SESSION['cart'] ?? [];
    error_log('Checkout page - Using raw session cart data');
}

// Debug cart contents
error_log('Checkout page - Final cart data: ' . print_r($cartItems, true));
error_log('Checkout page - Session cart data: ' . print_r($_SESSION['cart'] ?? [], true));

$subtotal = 0;
foreach ($cartItems as $item) {
    // Ensure $item is an array and has the required keys
    if (is_array($item) && isset($item['price']) && isset($item['quantity'])) {
        $subtotal += (float)$item['price'] * (int)$item['quantity'];
        error_log('Processing item for subtotal: ' . print_r($item, true));
    } else {
        error_log('Skipping invalid item: ' . print_r($item, true));
    }
}

// Calculate totals
$discount = max(0, (float)($_SESSION['discount'] ?? 0));
$shipping = 9.99; // Default shipping
$taxableAmount = max(0, $subtotal - $discount);
$tax = $taxableAmount * 0.08; // 8% tax
$total = max(0, $subtotal - $discount + $shipping + $tax);

// Count valid cart items
$validItemCount = 0;
foreach ($cartItems as $index => $item) {
    error_log("Checking item $index: " . print_r($item, true));

    if (is_array($item) && isset($item['name'], $item['price'], $item['quantity']) && $item['quantity'] > 0) {
        $validItemCount++;
        error_log("Item $index is valid, count now: $validItemCount");
    } else {
        $reasons = [];
        if (!is_array($item)) $reasons[] = 'not array';
        if (!isset($item['name'])) $reasons[] = 'missing name';
        if (!isset($item['price'])) $reasons[] = 'missing price';
        if (!isset($item['quantity'])) $reasons[] = 'missing quantity';
        if (isset($item['quantity']) && $item['quantity'] <= 0) $reasons[] = 'quantity <= 0';

        error_log("Item $index is invalid: " . implode(', ', $reasons));
    }
}

// Only redirect if cart is truly empty (no valid items)
if ($validItemCount === 0) {
    // Enable debugging to see what's happening
    error_log('Redirecting from checkout: cart is empty or all items have zero quantity.');
    error_log('Cart contents: ' . print_r($cartItems, true));
    error_log('Valid item count: ' . $validItemCount);

    // TEMPORARY: Show debug info instead of redirecting (remove this after debugging)
    if (isset($_GET['debug'])) {
        echo "<h2>DEBUG MODE - Cart is empty, would normally redirect</h2>";
        echo "<h3>Cart Items:</h3><pre>" . print_r($cartItems, true) . "</pre>";
        echo "<h3>Session Cart:</h3><pre>" . print_r($_SESSION['cart'] ?? [], true) . "</pre>";
        echo "<h3>Valid Item Count: $validItemCount</h3>";
        echo '<p><a href="' . UrlHelper::url('/cart') . '">Go to Cart</a></p>';
        echo '<p><a href="' . UrlHelper::url('/checkout') . '">Try Checkout Again</a></p>';
        exit;
    }

    // Check if headers have already been sent
    if (headers_sent($file, $line)) {
        error_log("Headers already sent in $file on line $line");
        // Use JavaScript redirect as fallback
        echo '<script>window.location.href = "' . UrlHelper::url('/cart') . '";</script>';
        echo '<noscript><meta http-equiv="refresh" content="0;url=' . UrlHelper::url('/cart') . '"></noscript>';
        exit;
    }

    // Use proper redirect
    header('Location: ' . UrlHelper::url('/cart'));
    exit;
}

// Get user info if logged in
$user = $_SESSION['user'] ?? [];

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Checkout - Premium Shopping Experience</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>

<body>

    <!-- Notification Area -->
    <div id="notification-area" aria-live="polite" class="position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999; margin-top: 20px;"></div>

    <!-- Processing Overlay -->
    <div id="processing-overlay" class="processing-overlay" style="display: none;">
        <div class="processing-backdrop"></div>
        <div class="processing-content">
            <div class="processing-animation">
                <div class="processing-circle"></div>
                <div class="processing-circle"></div>
                <div class="processing-circle"></div>
            </div>
            <h3 class="processing-title">Processing Your Order</h3>
            <p class="processing-subtitle">Please wait while we securely process your payment...</p>
            <div class="processing-steps">
                <div class="processing-step active">
                    <i class="fas fa-credit-card"></i>
                    <span>Validating Payment</span>
                </div>
                <div class="processing-step">
                    <i class="fas fa-box"></i>
                    <span>Creating Order</span>
                </div>
                <div class="processing-step">
                    <i class="fas fa-check-circle"></i>
                    <span>Confirmation</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Checkout Container -->
    <div class="checkout-container">
        <div class="container-fluid">
            <div class="row g-0 min-vh-100">

                <!-- Left Side - Checkout Form -->
                <div class="col-lg-8 checkout-form-section">
                    <div class="checkout-form-wrapper">

                        <!-- Header -->
                        <div class="checkout-header">
                            <div class="container">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h1 class="checkout-title">
                                            <i class="fas fa-shield-alt me-3"></i>
                                            Secure Checkout
                                        </h1>
                                        <p class="checkout-subtitle">Complete your purchase with confidence</p>
                                    </div>
                                    <div class="col-auto">
                                        <div class="security-indicators">
                                            <div class="security-badge">
                                                <i class="fas fa-lock"></i>
                                                <span>SSL Secured</span>
                                            </div>
                                            <div class="security-badge">
                                                <i class="fas fa-shield-check"></i>
                                                <span>256-bit Encryption</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Stepper -->
                        <div class="stepper-section">
                            <div class="container">
                                <div class="stepper-container">
                                    <div class="stepper-progress-track">
                                        <div class="stepper-progress-bar" id="progressBar"></div>
                                    </div>
                                    <div class="stepper-steps">
                                        <div class="stepper-step active" data-step="1" id="step-1">
                                            <div class="step-indicator">
                                                <div class="step-number">1</div>
                                                <div class="step-check"><i class="fas fa-check"></i></div>
                                            </div>
                                            <div class="step-info">
                                                <h6 class="step-title">Billing Information</h6>
                                                <p class="step-description">Your contact details</p>
                                            </div>
                                        </div>
                                        <div class="stepper-step" data-step="2" id="step-2">
                                            <div class="step-indicator">
                                                <div class="step-number">2</div>
                                                <div class="step-check"><i class="fas fa-check"></i></div>
                                            </div>
                                            <div class="step-info">
                                                <h6 class="step-title">Shipping Details</h6>
                                                <p class="step-description">Delivery information</p>
                                            </div>
                                        </div>
                                        <div class="stepper-step" data-step="3" id="step-3">
                                            <div class="step-indicator">
                                                <div class="step-number">3</div>
                                                <div class="step-check"><i class="fas fa-check"></i></div>
                                            </div>
                                            <div class="step-info">
                                                <h6 class="step-title">Payment Method</h6>
                                                <p class="step-description">Secure payment</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Content -->
                        <div class="form-content">
                            <div class="container">
                                <form action="<?= UrlHelper::url('/checkout') ?>" method="POST" id="checkoutForm" novalidate>
                                    <!-- CSRF Token -->
                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

                                    <!-- Step 1: Billing Information -->
                                    <div class="checkout-step active" data-step="1" id="billing-step">
                                        <div class="step-header">
                                            <h3 class="step-heading">
                                                <i class="fas fa-user-circle me-3"></i>
                                                Billing Information
                                            </h3>
                                            <p class="step-description">Please provide your billing details for this order</p>
                                        </div>

                                        <div class="form-grid">
                                            <div class="form-group">
                                                <label for="first_name" class="form-label">
                                                    <i class="fas fa-user me-2"></i>First Name *
                                                </label>
                                                <input type="text" class="form-control" id="first_name" name="first_name"
                                                    value="<?= htmlspecialchars($user['first_name'] ?? '') ?>"
                                                    placeholder="Enter your first name" required>
                                                <div class="invalid-feedback">Please enter your first name.</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="last_name" class="form-label">
                                                    <i class="fas fa-user me-2"></i>Last Name *
                                                </label>
                                                <input type="text" class="form-control" id="last_name" name="last_name"
                                                    value="<?= htmlspecialchars($user['last_name'] ?? '') ?>"
                                                    placeholder="Enter your last name" required>
                                                <div class="invalid-feedback">Please enter your last name.</div>
                                            </div>

                                            <div class="form-group full-width">
                                                <label for="email" class="form-label">
                                                    <i class="fas fa-envelope me-2"></i>Email Address *
                                                </label>
                                                <input type="email" class="form-control" id="email" name="email"
                                                    value="<?= htmlspecialchars($user['email'] ?? '') ?>"
                                                    placeholder="<EMAIL>" required>
                                                <div class="invalid-feedback">Please enter a valid email address.</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="phone" class="form-label">
                                                    <i class="fas fa-phone me-2"></i>Phone Number
                                                </label>
                                                <input type="tel" class="form-control" id="phone" name="phone"
                                                    value="<?= htmlspecialchars($user['phone'] ?? '') ?>"
                                                    placeholder="+****************">
                                                <div class="invalid-feedback">Please enter a valid phone number.</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="company" class="form-label">
                                                    <i class="fas fa-building me-2"></i>Company (Optional)
                                                </label>
                                                <input type="text" class="form-control" id="company" name="company"
                                                    placeholder="Your company name">
                                            </div>

                                            <div class="form-group full-width">
                                                <label for="address" class="form-label">
                                                    <i class="fas fa-map-marker-alt me-2"></i>Street Address *
                                                </label>
                                                <input type="text" class="form-control" id="address" name="address"
                                                    placeholder="123 Main Street, Apt 4B" required>
                                                <div class="invalid-feedback">Please enter your street address.</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="city" class="form-label">
                                                    <i class="fas fa-city me-2"></i>City *
                                                </label>
                                                <input type="text" class="form-control" id="city" name="city"
                                                    placeholder="New York" required>
                                                <div class="invalid-feedback">Please enter your city.</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="state" class="form-label">
                                                    <i class="fas fa-map me-2"></i>State / Province *
                                                </label>
                                                <select class="form-control" id="state" name="state" required>
                                                    <option value="">Select State</option>
                                                    <option value="AL">Alabama</option>
                                                    <option value="AK">Alaska</option>
                                                    <option value="AZ">Arizona</option>
                                                    <option value="AR">Arkansas</option>
                                                    <option value="CA">California</option>
                                                    <option value="CO">Colorado</option>
                                                    <option value="CT">Connecticut</option>
                                                    <option value="DE">Delaware</option>
                                                    <option value="FL">Florida</option>
                                                    <option value="GA">Georgia</option>
                                                    <!-- Add more states as needed -->
                                                </select>
                                                <div class="invalid-feedback">Please select your state.</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="zip_code" class="form-label">
                                                    <i class="fas fa-mail-bulk me-2"></i>ZIP / Postal Code *
                                                </label>
                                                <input type="text" class="form-control" id="zip_code" name="zip_code"
                                                    placeholder="12345" required>
                                                <div class="invalid-feedback">Please enter your ZIP code.</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="country" class="form-label">
                                                    <i class="fas fa-globe me-2"></i>Country *
                                                </label>
                                                <select class="form-control" id="country" name="country" required>
                                                    <option value="US" selected>United States</option>
                                                    <option value="CA">Canada</option>
                                                    <option value="GB">United Kingdom</option>
                                                    <!-- Add more countries as needed -->
                                                </select>
                                                <div class="invalid-feedback">Please select your country.</div>
                                            </div>
                                        </div>

                                        <div class="step-actions">
                                            <button type="button" class="btn btn-primary btn-next" data-next="2">
                                                Continue to Shipping <i class="fas fa-arrow-right ms-2"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Step 2: Shipping Information -->
                                    <div class="checkout-step" data-step="2" id="shipping-step">
                                        <div class="step-header">
                                            <h3 class="step-heading">
                                                <i class="fas fa-shipping-fast me-3"></i>
                                                Shipping Information
                                            </h3>
                                            <p class="step-description">Where should we deliver your order?</p>
                                        </div>

                                        <div class="shipping-options mb-4">
                                            <div class="form-check custom-checkbox">
                                                <input class="form-check-input" type="checkbox" id="same_as_billing" name="same_as_billing">
                                                <label class="form-check-label" for="same_as_billing">
                                                    <i class="fas fa-copy me-2"></i>
                                                    Use billing address for shipping
                                                </label>
                                            </div>
                                        </div>

                                        <div class="shipping-form" id="shipping-form">
                                            <div class="form-grid">
                                                <div class="form-group">
                                                    <label for="shipping_first_name" class="form-label">
                                                        <i class="fas fa-user me-2"></i>First Name *
                                                    </label>
                                                    <input type="text" class="form-control" id="shipping_first_name" name="shipping_first_name"
                                                        placeholder="Enter first name" required>
                                                    <div class="invalid-feedback">Please enter the first name.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="shipping_last_name" class="form-label">
                                                        <i class="fas fa-user me-2"></i>Last Name *
                                                    </label>
                                                    <input type="text" class="form-control" id="shipping_last_name" name="shipping_last_name"
                                                        placeholder="Enter last name" required>
                                                    <div class="invalid-feedback">Please enter the last name.</div>
                                                </div>

                                                <div class="form-group full-width">
                                                    <label for="shipping_address" class="form-label">
                                                        <i class="fas fa-map-marker-alt me-2"></i>Street Address *
                                                    </label>
                                                    <input type="text" class="form-control" id="shipping_address" name="shipping_address"
                                                        placeholder="123 Main Street, Apt 4B" required>
                                                    <div class="invalid-feedback">Please enter the street address.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="shipping_city" class="form-label">
                                                        <i class="fas fa-city me-2"></i>City *
                                                    </label>
                                                    <input type="text" class="form-control" id="shipping_city" name="shipping_city"
                                                        placeholder="New York" required>
                                                    <div class="invalid-feedback">Please enter the city.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="shipping_state" class="form-label">
                                                        <i class="fas fa-map me-2"></i>State / Province *
                                                    </label>
                                                    <select class="form-control" id="shipping_state" name="shipping_state" required>
                                                        <option value="">Select State</option>
                                                        <option value="AL">Alabama</option>
                                                        <option value="AK">Alaska</option>
                                                        <option value="AZ">Arizona</option>
                                                        <option value="AR">Arkansas</option>
                                                        <option value="CA">California</option>
                                                        <option value="CO">Colorado</option>
                                                        <option value="CT">Connecticut</option>
                                                        <option value="DE">Delaware</option>
                                                        <option value="FL">Florida</option>
                                                        <option value="GA">Georgia</option>
                                                        <!-- Add more states as needed -->
                                                    </select>
                                                    <div class="invalid-feedback">Please select the state.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="shipping_zip_code" class="form-label">
                                                        <i class="fas fa-mail-bulk me-2"></i>ZIP / Postal Code *
                                                    </label>
                                                    <input type="text" class="form-control" id="shipping_zip_code" name="shipping_zip_code"
                                                        placeholder="12345" required>
                                                    <div class="invalid-feedback">Please enter the ZIP code.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="shipping_country" class="form-label">
                                                        <i class="fas fa-globe me-2"></i>Country *
                                                    </label>
                                                    <select class="form-control" id="shipping_country" name="shipping_country" required>
                                                        <option value="US" selected>United States</option>
                                                        <option value="CA">Canada</option>
                                                        <option value="GB">United Kingdom</option>
                                                        <!-- Add more countries as needed -->
                                                    </select>
                                                    <div class="invalid-feedback">Please select the country.</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="delivery-options">
                                            <h5 class="delivery-title">
                                                <i class="fas fa-truck me-2"></i>
                                                Delivery Options
                                            </h5>
                                            <div class="delivery-methods">
                                                <div class="delivery-method">
                                                    <input type="radio" class="form-check-input" id="standard_delivery"
                                                        name="delivery_method" value="standard" checked>
                                                    <label for="standard_delivery" class="delivery-label">
                                                        <div class="method-info">
                                                            <h6 class="method-name">Standard Delivery</h6>
                                                            <p class="method-description">5-7 business days</p>
                                                        </div>
                                                        <div class="method-price">Free</div>
                                                    </label>
                                                </div>
                                                <div class="delivery-method">
                                                    <input type="radio" class="form-check-input" id="express_delivery"
                                                        name="delivery_method" value="express">
                                                    <label for="express_delivery" class="delivery-label">
                                                        <div class="method-info">
                                                            <h6 class="method-name">Express Delivery</h6>
                                                            <p class="method-description">2-3 business days</p>
                                                        </div>
                                                        <div class="method-price">$9.99</div>
                                                    </label>
                                                </div>
                                                <div class="delivery-method">
                                                    <input type="radio" class="form-check-input" id="overnight_delivery"
                                                        name="delivery_method" value="overnight">
                                                    <label for="overnight_delivery" class="delivery-label">
                                                        <div class="method-info">
                                                            <h6 class="method-name">Overnight Delivery</h6>
                                                            <p class="method-description">Next business day</p>
                                                        </div>
                                                        <div class="method-price">$24.99</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="step-actions">
                                            <button type="button" class="btn btn-outline-secondary btn-prev" data-prev="1">
                                                <i class="fas fa-arrow-left me-2"></i>
                                                Back to Billing
                                            </button>
                                            <button type="button" class="btn btn-primary btn-next" data-next="3">
                                                Continue to Payment <i class="fas fa-arrow-right ms-2"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Step 3: Payment Information -->
                                    <div class="checkout-step" data-step="3" id="payment-step">
                                        <div class="step-header">
                                            <h3 class="step-heading">
                                                <i class="fas fa-credit-card me-3"></i>
                                                Payment Information
                                            </h3>
                                            <p class="step-description">Secure payment processing with SSL encryption</p>
                                        </div>

                                        <div class="payment-methods mb-4">
                                            <h5 class="payment-title">
                                                <i class="fas fa-wallet me-2"></i>
                                                Payment Method
                                            </h5>
                                            <div class="payment-options">
                                                <div class="payment-option">
                                                    <input type="radio" class="form-check-input" id="credit_card"
                                                        name="payment_method" value="credit_card" checked>
                                                    <label for="credit_card" class="payment-label">
                                                        <div class="payment-info">
                                                            <i class="fas fa-credit-card payment-icon"></i>
                                                            <span class="payment-name">Credit / Debit Card</span>
                                                        </div>
                                                        <div class="card-types">
                                                            <i class="fab fa-cc-visa"></i>
                                                            <i class="fab fa-cc-mastercard"></i>
                                                            <i class="fab fa-cc-amex"></i>
                                                            <i class="fab fa-cc-discover"></i>
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="payment-option">
                                                    <input type="radio" class="form-check-input" id="paypal"
                                                        name="payment_method" value="paypal">
                                                    <label for="paypal" class="payment-label">
                                                        <div class="payment-info">
                                                            <i class="fab fa-paypal payment-icon"></i>
                                                            <span class="payment-name">PayPal</span>
                                                        </div>
                                                        <div class="payment-description">Pay with your PayPal account</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="credit-card-form" id="credit-card-form">
                                            <div class="form-grid">
                                                <div class="form-group full-width">
                                                    <label for="card_number" class="form-label">
                                                        <i class="fas fa-credit-card me-2"></i>Card Number *
                                                    </label>
                                                    <input type="text" class="form-control" id="card_number" name="card_number"
                                                        placeholder="1234 5678 9012 3456" maxlength="19" required>
                                                    <div class="invalid-feedback">Please enter a valid card number.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="card_name" class="form-label">
                                                        <i class="fas fa-user me-2"></i>Cardholder Name *
                                                    </label>
                                                    <input type="text" class="form-control" id="card_name" name="card_name"
                                                        placeholder="John Doe" required>
                                                    <div class="invalid-feedback">Please enter the cardholder name.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="card_expiry" class="form-label">
                                                        <i class="fas fa-calendar me-2"></i>Expiry Date *
                                                    </label>
                                                    <input type="text" class="form-control" id="card_expiry" name="card_expiry"
                                                        placeholder="MM/YY" maxlength="5" required>
                                                    <div class="invalid-feedback">Please enter the expiry date.</div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="card_cvv" class="form-label">
                                                        <i class="fas fa-lock me-2"></i>CVV *
                                                    </label>
                                                    <input type="text" class="form-control" id="card_cvv" name="card_cvv"
                                                        placeholder="123" maxlength="4" required>
                                                    <div class="invalid-feedback">Please enter the CVV.</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="order-notes mb-4">
                                            <label for="notes" class="form-label">
                                                <i class="fas fa-sticky-note me-2"></i>Order Notes (Optional)
                                            </label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                                placeholder="Any special instructions for your order..."></textarea>
                                        </div>

                                        <div class="terms-agreement mb-4">
                                            <div class="form-check custom-checkbox">
                                                <input class="form-check-input" type="checkbox" id="terms_agreement"
                                                    name="terms_agreement" required>
                                                <label class="form-check-label" for="terms_agreement">
                                                    I agree to the <a href="#" class="terms-link">Terms of Service</a> and
                                                    <a href="#" class="terms-link">Privacy Policy</a> *
                                                </label>
                                                <div class="invalid-feedback">You must agree to the terms and conditions.</div>
                                            </div>
                                        </div>

                                        <div class="step-actions">
                                            <button type="button" class="btn btn-outline-secondary btn-prev" data-prev="2">
                                                <i class="fas fa-arrow-left me-2"></i>
                                                Back to Shipping
                                            </button>
                                            <button type="submit" class="btn btn-success btn-place-order">
                                                <i class="fas fa-lock me-2"></i>
                                                Place Secure Order
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Order Summary -->
            <div class="col-lg-4 order-summary-section">
                <div class="order-summary-wrapper">
                    <div class="order-summary-sticky">

                        <!-- Order Summary Header -->
                        <div class="summary-header">
                            <h3 class="summary-title">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Order Summary
                            </h3>
                            <div class="summary-badge">
                                <span class="item-count"><?= $validItemCount ?> items</span>
                            </div>
                        </div>

                        <!-- Cart Items -->
                        <div class="cart-items">
                            <?php if (!empty($cartItems)): ?>
                                <?php foreach ($cartItems as $item): ?>
                                    <?php if (is_array($item) && isset($item['name'], $item['price'], $item['quantity'])): ?>
                                        <div class="cart-item">
                                            <div class="item-image">
                                                <img src="<?= htmlspecialchars($item['image'] ?? '/assets/images/placeholder.jpg') ?>"
                                                    alt="<?= htmlspecialchars($item['name']) ?>" class="img-fluid">
                                                <span class="item-quantity"><?= (int)$item['quantity'] ?></span>
                                            </div>
                                            <div class="item-details">
                                                <h6 class="item-name"><?= htmlspecialchars($item['name']) ?></h6>
                                                <p class="item-description"><?= htmlspecialchars($item['description'] ?? '') ?></p>
                                                <div class="item-price">
                                                    <span class="price">$<?= number_format((float)$item['price'], 2) ?></span>
                                                    <?php if ((int)$item['quantity'] > 1): ?>
                                                        <span class="quantity-price">× <?= (int)$item['quantity'] ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="item-total">
                                                $<?= number_format((float)$item['price'] * (int)$item['quantity'], 2) ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="empty-cart">
                                    <i class="fas fa-shopping-cart"></i>
                                    <p>Your cart is empty</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Discount Code Section -->
                        <div class="discount-section">
                            <div class="discount-toggle" onclick="toggleDiscountForm()">
                                <i class="fas fa-tag me-2"></i>
                                <span>Have a discount code?</span>
                                <i class="fas fa-chevron-down toggle-icon"></i>
                            </div>
                            <div class="discount-form" id="discount-form" style="display: none;">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="discount_code"
                                        placeholder="Enter discount code">
                                    <button class="btn btn-outline-primary" type="button" onclick="applyDiscount()">
                                        Apply
                                    </button>
                                </div>
                                <div id="discount-message" class="mt-2"></div>
                            </div>

                            <?php if (!empty($_SESSION['discount_code']) && $discount > 0): ?>
                                <div class="applied-discount">
                                    <div class="discount-info">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span class="discount-code"><?= htmlspecialchars($_SESSION['discount_code']) ?></span>
                                        <span class="discount-amount">-$<?= number_format($discount, 2) ?></span>
                                    </div>
                                    <button class="btn-remove-discount" onclick="removeDiscount()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Order Totals -->
                        <div class="order-totals">
                            <div class="total-row">
                                <span class="total-label">Subtotal</span>
                                <span class="total-value">$<?= number_format($subtotal, 2) ?></span>
                            </div>

                            <?php if ($discount > 0): ?>
                                <div class="total-row discount-row">
                                    <span class="total-label">Discount</span>
                                    <span class="total-value text-success">-$<?= number_format($discount, 2) ?></span>
                                </div>
                            <?php endif; ?>

                            <div class="total-row">
                                <span class="total-label">Shipping</span>
                                <span class="total-value" id="shipping-cost">
                                    <?= $shipping > 0 ? '$' . number_format($shipping, 2) : 'Free' ?>
                                </span>
                            </div>

                            <div class="total-row">
                                <span class="total-label">Tax</span>
                                <span class="total-value">$<?= number_format($tax, 2) ?></span>
                            </div>

                            <div class="total-row total-final">
                                <span class="total-label">Total</span>
                                <span class="total-value">$<?= number_format($total, 2) ?></span>
                            </div>
                        </div>

                        <!-- Delivery Information -->
                        <div class="delivery-info">
                            <div class="delivery-item">
                                <i class="fas fa-truck me-2"></i>
                                <div class="delivery-details">
                                    <span class="delivery-method" id="delivery-method">Standard Delivery</span>
                                    <span class="delivery-date" id="delivery-date">
                                        <?= date('M j, Y', strtotime('+5 days')) ?> - <?= date('M j, Y', strtotime('+7 days')) ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Security Badges -->
                        <div class="security-section">
                            <div class="security-badges">
                                <div class="security-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Secure Checkout</span>
                                </div>
                                <div class="security-item">
                                    <i class="fas fa-lock"></i>
                                    <span>SSL Protected</span>
                                </div>
                                <div class="security-item">
                                    <i class="fas fa-undo"></i>
                                    <span>30-Day Returns</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>
    </div>

    <style>
        /* Modern Checkout Styles */
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Processing Overlay */
        .processing-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .processing-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .processing-content {
            position: relative;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
        }

        .processing-animation {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 2rem;
        }

        .processing-circle {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            animation: processingPulse 1.4s ease-in-out infinite both;
        }

        .processing-circle:nth-child(1) {
            animation-delay: -0.32s;
        }

        .processing-circle:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes processingPulse {

            0%,
            80%,
            100% {
                transform: scale(0);
            }

            40% {
                transform: scale(1);
            }
        }

        .processing-title {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .processing-subtitle {
            color: #718096;
            margin-bottom: 2rem;
        }

        .processing-steps {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .processing-step {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-radius: 10px;
            background: #f7fafc;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .processing-step.active {
            opacity: 1;
            background: #e6fffa;
            color: #38b2ac;
        }

        .processing-step.completed {
            opacity: 1;
            background: #f0fff4;
            color: #38a169;
        }

        /* Main Container */
        .checkout-container {
            min-height: 100vh;
        }

        .checkout-container .row {
            display: flex;
            flex-wrap: nowrap;
        }

        /* Left Side - Form Section */
        .checkout-form-section {
            background: white;
            position: relative;
        }

        .checkout-form-wrapper {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .checkout-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }

        .checkout-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .checkout-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
        }

        .security-indicators {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .security-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }

        /* Stepper */
        .stepper-section {
            background: #f8fafc;
            padding: 2rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .stepper-container {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .stepper-progress-track {
            position: absolute;
            top: 30px;
            left: 0;
            right: 0;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            z-index: 1;
        }

        .stepper-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            width: 33.33%;
            transition: width 0.5s ease;
        }

        .stepper-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 2;
        }

        .stepper-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            flex: 1;
            max-width: 200px;
        }

        .step-indicator {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: white;
            border: 4px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .stepper-step.active .step-indicator {
            border-color: #667eea;
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .stepper-step.completed .step-indicator {
            border-color: #38a169;
            background: #38a169;
            color: white;
        }

        .step-number {
            font-weight: 600;
            font-size: 1.2rem;
        }

        .step-check {
            display: none;
        }

        .stepper-step.completed .step-number {
            display: none;
        }

        .stepper-step.completed .step-check {
            display: block;
        }

        .step-info {
            text-align: center;
        }

        .step-title {
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
        }

        .step-description {
            color: #718096;
            font-size: 0.875rem;
            margin: 0;
        }

        /* Form Content */
        .form-content {
            flex: 1;
            padding: 3rem 0;
        }

        .checkout-step {
            display: none;
        }

        .checkout-step.active {
            display: block;
        }

        .step-header {
            margin-bottom: 2rem;
            text-align: center;
        }

        .step-heading {
            font-size: 1.75rem;
            font-weight: 700;
            color: #2d3748;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .step-description {
            color: #718096;
            font-size: 1rem;
            margin: 0;
        }

        /* Form Grid */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group {
            position: relative;
        }

        .form-label {
            display: flex;
            align-items: center;
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
        }

        .form-control.is-invalid {
            border-color: #e53e3e;
            background: #fed7d7;
        }

        .form-control.is-valid {
            border-color: #38a169;
            background: #f0fff4;
        }

        .invalid-feedback {
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: none;
        }

        .form-control.is-invalid+.invalid-feedback {
            display: block;
        }

        /* Custom Checkbox */
        .custom-checkbox .form-check-input {
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
        }

        .custom-checkbox .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .custom-checkbox .form-check-label {
            font-weight: 500;
            color: #2d3748;
            margin-left: 0.5rem;
        }

        /* Delivery Options */
        .delivery-options {
            margin-top: 2rem;
        }

        .delivery-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .delivery-methods {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .delivery-method {
            position: relative;
        }

        .delivery-method input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .delivery-label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .delivery-method input[type="radio"]:checked+.delivery-label {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .method-info {
            flex: 1;
        }

        .method-name {
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
        }

        .method-description {
            color: #718096;
            margin: 0;
            font-size: 0.875rem;
        }

        .method-price {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1rem;
        }

        /* Payment Options */
        .payment-methods {
            margin-bottom: 2rem;
        }

        .payment-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .payment-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .payment-option {
            position: relative;
        }

        .payment-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .payment-label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .payment-option input[type="radio"]:checked+.payment-label {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .payment-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .payment-icon {
            font-size: 1.5rem;
            color: #667eea;
        }

        .payment-name {
            font-weight: 600;
            color: #2d3748;
        }

        .payment-description {
            color: #718096;
            font-size: 0.875rem;
        }

        .card-types {
            display: flex;
            gap: 0.5rem;
        }

        .card-types i {
            font-size: 1.5rem;
            color: #718096;
        }

        /* Step Actions */
        .step-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }

        .btn {
            padding: 0.875rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-outline-secondary {
            background: transparent;
            color: #718096;
            border: 2px solid #e2e8f0;
        }

        .btn-outline-secondary:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
        }

        .btn-success {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(56, 161, 105, 0.3);
        }

        .btn-place-order {
            font-size: 1.1rem;
            padding: 1rem 2.5rem;
        }

        /* Right Side - Order Summary */
        .order-summary-section {
            background: #f8fafc;
            border-left: 1px solid #e2e8f0;
            order: 2;
            /* Ensure it stays on the right on desktop */
        }

        .checkout-form-section {
            order: 1;
            /* Ensure form stays on the left on desktop */
        }

        .order-summary-wrapper {
            height: 100%;
            padding: 2rem;
        }

        .order-summary-sticky {
            position: sticky;
            top: 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* Summary Header */
        .summary-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .summary-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .summary-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            backdrop-filter: blur(10px);
        }

        /* Cart Items */
        .cart-items {
            max-height: 300px;
            overflow-y: auto;
            padding: 1.5rem;
        }

        .cart-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .item-image {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 10px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .item-quantity {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #667eea;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 0.25rem 0;
            font-size: 0.95rem;
        }

        .item-description {
            color: #718096;
            font-size: 0.8rem;
            margin: 0 0 0.5rem 0;
        }

        .item-price {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .price {
            font-weight: 600;
            color: #2d3748;
        }

        .quantity-price {
            color: #718096;
            font-size: 0.875rem;
        }

        .item-total {
            font-weight: 700;
            color: #2d3748;
            font-size: 1rem;
        }

        .empty-cart {
            text-align: center;
            padding: 2rem;
            color: #718096;
        }

        .empty-cart i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Discount Section */
        .discount-section {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
        }

        .discount-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            color: #667eea;
            font-weight: 500;
            padding: 0.5rem 0;
        }

        .discount-toggle:hover {
            color: #5a67d8;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .discount-toggle.active .toggle-icon {
            transform: rotate(180deg);
        }

        .discount-form {
            margin-top: 1rem;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .applied-discount {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .discount-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .discount-code {
            font-weight: 600;
            color: #2f855a;
        }

        .discount-amount {
            font-weight: 700;
            color: #2f855a;
        }

        .btn-remove-discount {
            background: none;
            border: none;
            color: #e53e3e;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .btn-remove-discount:hover {
            background: #fed7d7;
        }

        /* Order Totals */
        .order-totals {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
        }

        .total-label {
            color: #4a5568;
            font-weight: 500;
        }

        .total-value {
            font-weight: 600;
            color: #2d3748;
        }

        .discount-row .total-value {
            color: #38a169;
        }

        .total-final {
            border-top: 2px solid #e2e8f0;
            margin-top: 0.5rem;
            padding-top: 1rem;
        }

        .total-final .total-label {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2d3748;
        }

        .total-final .total-value {
            font-size: 1.25rem;
            font-weight: 800;
            color: #667eea;
        }

        /* Delivery Info */
        .delivery-info {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
        }

        .delivery-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .delivery-details {
            display: flex;
            flex-direction: column;
        }

        .delivery-method {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }

        .delivery-date {
            color: #718096;
            font-size: 0.875rem;
        }

        /* Security Section */
        .security-section {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
            background: #f8fafc;
        }

        .security-badges {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .security-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #4a5568;
            font-size: 0.875rem;
        }

        .security-item i {
            color: #38a169;
            width: 16px;
        }

        /* Responsive Design */
        @media (max-width: 991.98px) {
            .checkout-container .row {
                flex-direction: column;
            }

            .checkout-form-section {
                order: 2 !important;
                /* Override desktop order for mobile */
            }

            .order-summary-section {
                order: 1 !important;
                /* Override desktop order for mobile */
                border-left: none;
                border-bottom: 1px solid #e2e8f0;
            }

            .order-summary-wrapper {
                padding: 1rem;
            }

            .order-summary-sticky {
                position: static;
                margin-bottom: 2rem;
            }

            .checkout-title {
                font-size: 2rem;
            }

            .stepper-steps {
                flex-direction: column;
                gap: 1rem;
            }

            .stepper-step {
                flex-direction: row;
                text-align: left;
                max-width: none;
            }

            .step-indicator {
                margin-bottom: 0;
                margin-right: 1rem;
                width: 50px;
                height: 50px;
            }

            .stepper-progress-track {
                display: none;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .step-actions {
                flex-direction: column;
                gap: 1rem;
            }

            .step-actions .btn {
                width: 100%;
                justify-content: center;
            }

            .security-indicators {
                flex-direction: row;
                gap: 1rem;
            }
        }

        @media (max-width: 575.98px) {
            .checkout-header {
                padding: 1.5rem 0;
            }

            .checkout-title {
                font-size: 1.75rem;
            }

            .security-indicators {
                flex-direction: column;
                gap: 0.5rem;
            }

            .form-content {
                padding: 2rem 0;
            }

            .cart-items {
                max-height: 200px;
            }
        }

        /* Terms Link */
        .terms-link {
            color: #667eea;
            text-decoration: none;
        }

        .terms-link:hover {
            text-decoration: underline;
        }

        /* Input Masks and Validation */
        .form-control:focus {
            transform: translateY(-1px);
        }

        .form-control.is-valid {
            border-color: #38a169;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2338a169' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class CheckoutManager {
            constructor() {
                this.currentStep = 1;
                this.totalSteps = 3;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupFormValidation();
                this.setupInputMasks();
                this.updateProgressBar();
            }

            setupEventListeners() {
                // Step navigation
                document.querySelectorAll('.btn-next').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const nextStep = parseInt(e.target.dataset.next);
                        this.goToStep(nextStep);
                    });
                });

                document.querySelectorAll('.btn-prev').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const prevStep = parseInt(e.target.dataset.prev);
                        this.goToStep(prevStep);
                    });
                });

                // Same as billing checkbox
                const sameAsBilling = document.getElementById('same_as_billing');
                if (sameAsBilling) {
                    sameAsBilling.addEventListener('change', this.handleSameAsBilling.bind(this));
                }

                // Payment method change
                document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        this.togglePaymentForm(e.target.value);
                    });
                });

                // Delivery method change
                document.querySelectorAll('input[name="delivery_method"]').forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        this.updateDeliveryInfo(e.target.value);
                    });
                });

                // Form submission
                const form = document.getElementById('checkoutForm');
                if (form) {
                    form.addEventListener('submit', this.handleFormSubmission.bind(this));
                }
            }

            goToStep(stepNumber) {
                if (stepNumber < 1 || stepNumber > this.totalSteps) return;

                // Validate current step before proceeding
                if (stepNumber > this.currentStep && !this.validateCurrentStep()) {
                    this.showNotification('Please complete all required fields before proceeding.', 'error');
                    return;
                }

                // Hide current step
                document.querySelector(`.checkout-step[data-step="${this.currentStep}"]`).classList.remove('active');
                document.querySelector(`#step-${this.currentStep}`).classList.remove('active');

                // Mark previous steps as completed
                if (stepNumber > this.currentStep) {
                    document.querySelector(`#step-${this.currentStep}`).classList.add('completed');
                }

                // Show new step
                this.currentStep = stepNumber;
                document.querySelector(`.checkout-step[data-step="${this.currentStep}"]`).classList.add('active');
                document.querySelector(`#step-${this.currentStep}`).classList.add('active');

                // Update progress bar
                this.updateProgressBar();

                // Scroll to top
                this.scrollToTop();
            }

            updateProgressBar() {
                const progressBar = document.getElementById('progressBar');
                if (progressBar) {
                    const progress = ((this.currentStep - 1) / (this.totalSteps - 1)) * 100;
                    progressBar.style.width = `${progress}%`;
                }
            }

            validateCurrentStep() {
                const currentStepElement = document.querySelector(`.checkout-step[data-step="${this.currentStep}"]`);
                if (!currentStepElement) return false;

                const requiredFields = currentStepElement.querySelectorAll('[required]:not([disabled])');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!this.validateField(field)) {
                        isValid = false;
                    }
                });

                // Special validation for step 2 (shipping)
                if (this.currentStep === 2) {
                    const sameAsBilling = document.getElementById('same_as_billing');
                    if (!sameAsBilling || !sameAsBilling.checked) {
                        // Validate shipping fields only if not using billing address
                        const shippingFields = currentStepElement.querySelectorAll('[id^="shipping_"][required]');
                        shippingFields.forEach(field => {
                            if (!this.validateField(field)) {
                                isValid = false;
                            }
                        });
                    }
                }

                return isValid;
            }

            validateField(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';

                // Required field validation
                if (field.hasAttribute('required') && !value) {
                    isValid = false;
                    errorMessage = 'This field is required.';
                }

                // Email validation
                if (field.type === 'email' && value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address.';
                    }
                }

                // Phone validation
                if (field.type === 'tel' && value) {
                    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
                    if (!phoneRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid phone number.';
                    }
                }

                // Credit card validation
                if (field.id === 'card_number' && value) {
                    const cardRegex = /^\d{4}\s\d{4}\s\d{4}\s\d{4}$/;
                    if (!cardRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid card number.';
                    }
                }

                // Expiry date validation
                if (field.id === 'card_expiry' && value) {
                    const expiryRegex = /^(0[1-9]|1[0-2])\/\d{2}$/;
                    if (!expiryRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid expiry date (MM/YY).';
                    }
                }

                // CVV validation
                if (field.id === 'card_cvv' && value) {
                    const cvvRegex = /^\d{3,4}$/;
                    if (!cvvRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid CVV.';
                    }
                }

                // Update field appearance
                if (isValid) {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                } else {
                    field.classList.remove('is-valid');
                    field.classList.add('is-invalid');

                    // Update error message
                    const feedback = field.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errorMessage;
                    }
                }

                return isValid;
            }

            setupInputMasks() {
                // Phone number mask
                const phoneField = document.getElementById('phone');
                if (phoneField) {
                    phoneField.addEventListener('input', (e) => {
                        let value = e.target.value.replace(/\D/g, '');
                        if (value.length >= 10) {
                            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                        }
                        e.target.value = value.substring(0, 14); // Limit length
                    });
                }

                // Credit card number mask
                const cardField = document.getElementById('card_number');
                if (cardField) {
                    cardField.addEventListener('input', (e) => {
                        let value = e.target.value.replace(/\D/g, '');
                        value = value.substring(0, 16); // Limit to 16 digits
                        value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
                        e.target.value = value;
                    });
                }

                // Expiry date mask
                const expiryField = document.getElementById('card_expiry');
                if (expiryField) {
                    expiryField.addEventListener('input', (e) => {
                        let value = e.target.value.replace(/\D/g, '');
                        if (value.length >= 2) {
                            value = value.replace(/(\d{2})(\d{0,2})/, '$1/$2');
                        }
                        e.target.value = value;
                    });
                }

                // CVV mask
                const cvvField = document.getElementById('card_cvv');
                if (cvvField) {
                    cvvField.addEventListener('input', (e) => {
                        let value = e.target.value.replace(/\D/g, '');
                        e.target.value = value.substring(0, 4); // Limit to 4 digits
                    });
                }
            }

            handleSameAsBilling() {
                const checkbox = document.getElementById('same_as_billing');
                const shippingForm = document.getElementById('shipping-form');

                if (checkbox && shippingForm) {
                    if (checkbox.checked) {
                        // Copy billing info to shipping
                        const billingFields = ['first_name', 'last_name', 'address', 'city', 'state', 'zip_code', 'country'];

                        billingFields.forEach(fieldName => {
                            const billingField = document.getElementById(fieldName);
                            const shippingField = document.getElementById(`shipping_${fieldName}`);

                            if (billingField && shippingField) {
                                shippingField.value = billingField.value;
                                // Remove validation classes
                                shippingField.classList.remove('is-invalid', 'is-valid');
                            }
                        });

                        // Disable shipping form
                        const shippingInputs = shippingForm.querySelectorAll('input, select');
                        shippingInputs.forEach(input => {
                            input.disabled = true;
                            input.removeAttribute('required');
                        });

                        shippingForm.style.opacity = '0.5';
                        shippingForm.style.pointerEvents = 'none';
                    } else {
                        // Enable shipping form
                        const shippingInputs = shippingForm.querySelectorAll('input, select');
                        shippingInputs.forEach(input => {
                            input.disabled = false;
                            if (input.dataset.originalRequired !== 'false') {
                                input.setAttribute('required', '');
                            }
                        });

                        shippingForm.style.opacity = '1';
                        shippingForm.style.pointerEvents = 'auto';
                    }
                }
            }

            togglePaymentForm(method) {
                const creditCardForm = document.getElementById('credit-card-form');
                if (method === 'credit_card') {
                    creditCardForm.style.display = 'block';
                } else {
                    creditCardForm.style.display = 'none';
                }
            }

            updateDeliveryInfo(method) {
                const deliveryDate = document.getElementById('delivery-date');
                const deliveryMethod = document.getElementById('delivery-method');
                const shippingCost = document.getElementById('shipping-cost');

                const deliveryOptions = {
                    'standard': {
                        date: '<?= date('M j, Y', strtotime('+5 days')) ?> - <?= date('M j, Y', strtotime('+7 days')) ?>',
                        method: 'Standard Delivery',
                        cost: 'Free'
                    },
                    'express': {
                        date: '<?= date('M j, Y', strtotime('+2 days')) ?> - <?= date('M j, Y', strtotime('+3 days')) ?>',
                        method: 'Express Delivery',
                        cost: '$9.99'
                    },
                    'overnight': {
                        date: '<?= date('M j, Y', strtotime('+1 day')) ?>',
                        method: 'Overnight Delivery',
                        cost: '$24.99'
                    }
                };

                const option = deliveryOptions[method];
                if (option) {
                    deliveryDate.textContent = option.date;
                    deliveryMethod.textContent = option.method;
                    shippingCost.textContent = option.cost;
                }
            }

            handleFormSubmission(e) {
                e.preventDefault();

                // Final validation
                if (!this.validateCurrentStep()) {
                    this.showNotification('Please complete all required fields.', 'error');
                    return;
                }

                // Show processing overlay
                document.getElementById('processing-overlay').style.display = 'flex';

                // Simulate processing steps
                this.simulateProcessing();

                // Submit form after delay
                setTimeout(() => {
                    e.target.submit();
                }, 3000);
            }

            simulateProcessing() {
                const steps = document.querySelectorAll('.processing-steps .processing-step');
                let currentStep = 0;

                const interval = setInterval(() => {
                    if (currentStep > 0) {
                        steps[currentStep - 1].classList.remove('active');
                        steps[currentStep - 1].classList.add('completed');
                    }

                    if (currentStep < steps.length) {
                        steps[currentStep].classList.add('active');
                        currentStep++;
                    } else {
                        clearInterval(interval);
                    }
                }, 1000);
            }

            setupFormValidation() {
                // Real-time validation
                document.querySelectorAll('input, select, textarea').forEach(field => {
                    field.addEventListener('blur', () => {
                        this.validateField(field);
                    });

                    field.addEventListener('input', () => {
                        if (field.classList.contains('is-invalid')) {
                            this.validateField(field);
                        }
                    });
                });
            }

            scrollToTop() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }

            showNotification(message, type = 'info') {
                const area = document.getElementById('notification-area');
                const alertClass = type === 'error' ? 'danger' : type;
                area.innerHTML = `
                    <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    const alert = area.querySelector('.alert');
                    if (alert) {
                        alert.remove();
                    }
                }, 5000);
            }
        }

        // Discount code functions
        function toggleDiscountForm() {
            const form = document.getElementById('discount-form');
            const toggle = document.querySelector('.discount-toggle');

            if (form.style.display === 'none' || !form.style.display) {
                form.style.display = 'block';
                toggle.classList.add('active');
            } else {
                form.style.display = 'none';
                toggle.classList.remove('active');
            }
        }

        function applyDiscount() {
            const code = document.getElementById('discount_code').value.trim();
            const messageDiv = document.getElementById('discount-message');

            if (!code) {
                messageDiv.innerHTML = '<div class="text-danger">Please enter a discount code.</div>';
                return;
            }

            fetch('<?= UrlHelper::url('/cart/apply-discount') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `discount_code=${encodeURIComponent(code)}&csrf_token=${encodeURIComponent('<?= $_SESSION['csrf_token'] ?>')}`
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        location.reload(); // Reload to show applied discount
                    } else {
                        messageDiv.innerHTML = `<div class="text-danger">${escapeHtml(data.message || 'Invalid discount code.')}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Discount error:', error);
                    messageDiv.innerHTML = '<div class="text-danger">An error occurred. Please try again.</div>';
                });
        }

        function removeDiscount() {
            fetch('<?= UrlHelper::url('/cart/remove-discount') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `csrf_token=${encodeURIComponent('<?= $_SESSION['csrf_token'] ?>')}`
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        location.reload(); // Reload to remove discount
                    }
                })
                .catch(error => {
                    console.error('Remove discount error:', error);
                });
        }

        // HTML escape utility function
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Initialize checkout manager when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            new CheckoutManager();
        });
    </script>

</body>

</html>
new CheckoutManager();
});
</script>

</body>

</html>