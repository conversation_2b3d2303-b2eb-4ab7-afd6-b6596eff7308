<!-- Notification Area -->
<div id="notification-area" aria-live="polite" class="mb-3"></div>
<div class="container mt-4">
    <div class="checkout-container">
        <div class="row g-4">
            <!-- Checkout Form -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow-sm mb-4 bg-light border-0">
                    <div class="card-header bg-primary text-white py-3">
                        <h4 class="mb-0 fw-bold">
                            <i class="fas fa-credit-card me-2"></i>Checkout
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <form action="/checkout" method="POST" id="checkoutForm" autocomplete="off">
                            <!-- Billing Information -->
                            <div class="mb-5">
                                <div class="section-header">
                                    <h5>
                                        <i class="fas fa-user"></i>Billing Information
                                    </h5>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="billing_first_name" class="form-label">First Name *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="billing_first_name" name="billing_first_name"
                                                value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="billing_last_name" class="form-label">Last Name *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="billing_last_name" name="billing_last_name"
                                                value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-3 mt-2">
                                    <div class="col-md-6">
                                        <label for="billing_email" class="form-label">Email Address *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            <input type="email" class="form-control" id="billing_email" name="billing_email"
                                                value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="billing_phone" class="form-label">Phone Number</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="tel" class="form-control" id="billing_phone" name="billing_phone"
                                                value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <label for="billing_address" class="form-label">Address *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                        <input type="text" class="form-control" id="billing_address" name="billing_address" required>
                                    </div>
                                </div>
                                <div class="row g-3 mt-2">
                                    <div class="col-md-6">
                                        <label for="billing_city" class="form-label">City *</label>
                                        <input type="text" class="form-control" id="billing_city" name="billing_city" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="billing_state" class="form-label">State/Province *</label>
                                        <input type="text" class="form-control" id="billing_state" name="billing_state" required>
                                    </div>
                                </div>
                                <div class="row g-3 mt-2">
                                    <div class="col-md-6">
                                        <label for="billing_postal_code" class="form-label">Postal Code *</label>
                                        <input type="text" class="form-control" id="billing_postal_code" name="billing_postal_code" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="billing_country" class="form-label">Country *</label>
                                        <input type="text" class="form-control" id="billing_country" name="billing_country" required>
                                    </div>
                                </div>
                            </div>
                            <!-- Shipping Information -->
                            <div class="mb-5 pb-3 border-bottom">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h5 class="mb-0 fw-semibold">
                                        <i class="fas fa-shipping-fast me-2"></i>Shipping Information
                                    </h5>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="same_as_billing" checked>
                                        <label class="form-check-label" for="same_as_billing">
                                            Same as billing address
                                        </label>
                                    </div>
                                </div>
                                <div id="shipping-fields" style="display: none;">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="shipping_first_name" class="form-label">First Name *</label>
                                            <input type="text" class="form-control" id="shipping_first_name" name="shipping_first_name">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="shipping_last_name" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control" id="shipping_last_name" name="shipping_last_name">
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <label for="shipping_address" class="form-label">Address *</label>
                                        <input type="text" class="form-control" id="shipping_address" name="shipping_address">
                                    </div>
                                    <div class="row g-3 mt-2">
                                        <div class="col-md-6">
                                            <label for="shipping_city" class="form-label">City *</label>
                                            <input type="text" class="form-control" id="shipping_city" name="shipping_city">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="shipping_state" class="form-label">State/Province *</label>
                                            <input type="text" class="form-control" id="shipping_state" name="shipping_state">
                                        </div>
                                    </div>
                                    <div class="row g-3 mt-2">
                                        <div class="col-md-6">
                                            <label for="shipping_postal_code" class="form-label">Postal Code *</label>
                                            <input type="text" class="form-control" id="shipping_postal_code" name="shipping_postal_code">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="shipping_country" class="form-label">Country *</label>
                                            <input type="text" class="form-control" id="shipping_country" name="shipping_country">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Payment Information -->
                            <div class="mb-5 pb-3 border-bottom">
                                <h5 class="mb-4 fw-semibold">
                                    <i class="fas fa-credit-card me-2"></i>Payment Information
                                </h5>
                                <div class="mb-3">
                                    <label for="card_number" class="form-label">Card Number *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                        <input type="text" class="form-control" id="card_number" name="card_number"
                                            placeholder="1234 5678 9012 3456" required maxlength="19">
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="card_expiry" class="form-label">Expiry Date *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                            <input type="text" class="form-control" id="card_expiry" name="card_expiry"
                                                placeholder="MM/YY" required maxlength="5">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="card_cvv" class="form-label">CVV *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="text" class="form-control" id="card_cvv" name="card_cvv"
                                                placeholder="123" required maxlength="4">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3 mt-3">
                                    <label for="card_name" class="form-label">Name on Card *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="card_name" name="card_name" required>
                                    </div>
                                </div>
                            </div>
                            <!-- Order Notes -->
                            <div class="mb-4">
                                <label for="order_notes" class="form-label">Order Notes (Optional)</label>
                                <textarea class="form-control" id="order_notes" name="order_notes" rows="3"
                                    placeholder="Any special instructions or notes for your order..."></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary btn-lg w-100 shadow-sm">
                                <i class="fas fa-lock me-2"></i>Place Order
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="order-summary-sticky">
                    <div class="card">
                        <div class="card-header">
                            <h4>
                                <i class="fas fa-shopping-cart me-2"></i>Order Summary
                            </h4>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($cartItems)): ?>
                                <?php foreach ($cartItems as $item): ?>
                                    <div class="order-item">
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo htmlspecialchars($item['image_path'] ?? '/assets/images/placeholder.jpg'); ?>"
                                                alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                class="product-image me-3">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 fw-semibold"><?php echo htmlspecialchars($item['name']); ?></h6>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">Qty: <?php echo $item['quantity']; ?></small>
                                                    <div class="text-end">
                                                        <div class="fw-bold text-primary">$<?php echo number_format(($item['sale_price'] ?? $item['price']) * $item['quantity'], 2); ?></div>
                                                        <?php if (isset($item['sale_price']) && $item['sale_price']): ?>
                                                            <small class="text-muted text-decoration-line-through">
                                                                $<?php echo number_format($item['price'] * $item['quantity'], 2); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                <hr>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal:</span>
                                    <span>$<?php echo number_format($subtotal, 2); ?></span>
                                </div>
                                <?php if (isset($discount) && $discount > 0): ?>
                                    <div class="d-flex justify-content-between mb-2 text-success">
                                        <span>Discount:</span>
                                        <span>-$<?php echo number_format($discount, 2); ?></span>
                                    </div>
                                <?php endif; ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Shipping:</span>
                                    <span>$<?php echo number_format($shipping, 2); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Tax:</span>
                                    <span>$<?php echo number_format($tax, 2); ?></span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold fs-5 bg-primary bg-opacity-10 rounded p-2 border border-primary">
                                    <span>Total:</span>
                                    <span class="text-primary">$<?php echo number_format($total, 2); ?></span>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-shopping-cart text-muted mb-3" style="font-size: 3rem;"></i>
                                    <h6 class="text-muted">Your cart is empty</h6>
                                    <a href="/products" class="btn btn-primary">Continue Shopping</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8/jquery.inputmask.min.js"></script>
        <script>
            // Notification helper
            function showNotification(message, type = 'info') {
                const area = document.getElementById('notification-area');
                area.innerHTML = `<div class="alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show" role="alert">${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>`;
            }
            // Input masks
            $(function() {
                $('#card_number').inputmask('9999 9999 9999 9999[ 9999 9999]');
                $('#card_expiry').inputmask('99/99');
                $('#billing_phone').inputmask('+9{1,3} ************');
            });
            // Toggle shipping fields
            document.getElementById('same_as_billing').addEventListener('change', function() {
                const shippingFields = document.getElementById('shipping-fields');
                const shippingInputs = shippingFields.querySelectorAll('input');
                if (this.checked) {
                    shippingFields.style.display = 'none';
                    shippingInputs.forEach(input => input.removeAttribute('required'));
                    // Copy billing to shipping
                    document.getElementById('shipping_first_name').value = document.getElementById('billing_first_name').value;
                    document.getElementById('shipping_last_name').value = document.getElementById('billing_last_name').value;
                    document.getElementById('shipping_address').value = document.getElementById('billing_address').value;
                    document.getElementById('shipping_city').value = document.getElementById('billing_city').value;
                    document.getElementById('shipping_state').value = document.getElementById('billing_state').value;
                    document.getElementById('shipping_postal_code').value = document.getElementById('billing_postal_code').value;
                    document.getElementById('shipping_country').value = document.getElementById('billing_country').value;
                } else {
                    shippingFields.style.display = 'block';
                    shippingInputs.forEach(input => input.setAttribute('required', 'required'));
                }
            });
            // Form validation
            document.getElementById('checkoutForm').addEventListener('submit', function(e) {
                const cardNumber = document.getElementById('card_number').value.replace(/\s/g, '');
                const cardExpiry = document.getElementById('card_expiry').value;
                const cardCvv = document.getElementById('card_cvv').value;
                // Basic card validation
                if (cardNumber.length < 13 || cardNumber.length > 19) {
                    e.preventDefault();
                    showNotification('Please enter a valid card number.', 'error');
                    return;
                }
                if (!/^\d{2}\/\d{2}$/.test(cardExpiry)) {
                    e.preventDefault();
                    showNotification('Please enter expiry date in MM/YY format.', 'error');
                    return;
                }
                if (cardCvv.length < 3 || cardCvv.length > 4) {
                    e.preventDefault();
                    showNotification('Please enter a valid CVV.', 'error');
                    return;
                }
                // Disable submit button to prevent double submission
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                // Show overlay
                document.getElementById('order-processing-overlay').style.display = 'block';
            });
        </script>

        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            }

            .checkout-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                margin: 20px auto;
                overflow: hidden;
            }

            .card {
                border: none;
                border-radius: 15px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.98);
            }

            .card:hover {
                transform: translateY(-2px);
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
            }

            .card-header {
                border-radius: 15px 15px 0 0 !important;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white !important;
                padding: 20px 25px;
            }

            .card-header h4 {
                margin: 0;
                font-weight: 600;
                font-size: 1.4rem;
            }

            .stepper {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 8px;
                margin-bottom: 30px;
            }

            .stepper .nav-link {
                border-radius: 8px;
                color: #6c757d;
                font-weight: 500;
                padding: 12px 20px;
                transition: all 0.3s ease;
                border: 2px solid transparent;
                position: relative;
                overflow: hidden;
            }

            .stepper .nav-link.active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-color: rgba(255, 255, 255, 0.2);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            }

            .stepper .nav-link:not(.active):hover {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
                transform: translateY(-1px);
            }

            .step-number {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.2);
                margin-right: 10px;
                font-weight: 600;
                font-size: 0.9rem;
            }

            .stepper .nav-link.active .step-number {
                background: rgba(255, 255, 255, 0.3);
            }

            .form-control {
                border-radius: 12px;
                border: 2px solid #e9ecef;
                padding: 15px 18px;
                background: rgba(255, 255, 255, 0.9);
                font-size: 0.95rem;
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
            }

            .form-control:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
                background: white;
                transform: translateY(-1px);
            }

            .form-control.is-invalid {
                border-color: #dc3545;
                box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
            }

            .input-group-text {
                border-radius: 12px 0 0 12px;
                border: 2px solid #e9ecef;
                border-right: none;
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
                font-weight: 500;
            }

            .input-group .form-control {
                border-radius: 0 12px 12px 0;
                border-left: none;
            }

            .input-group:focus-within .input-group-text {
                border-color: #667eea;
                background: rgba(102, 126, 234, 0.15);
            }

            .btn {
                border-radius: 12px;
                font-weight: 600;
                padding: 12px 24px;
                transition: all 0.3s ease;
                border: none;
                position: relative;
                overflow: hidden;
            }

            .btn-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }

            .btn-outline-secondary {
                border: 2px solid #6c757d;
                color: #6c757d;
                background: transparent;
            }

            .btn-outline-secondary:hover {
                background: #6c757d;
                color: white;
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
            }

            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none !important;
                box-shadow: none !important;
            }

            .form-check-input {
                border-radius: 6px;
                border: 2px solid #e9ecef;
                width: 1.2em;
                height: 1.2em;
            }

            .form-check-input:checked {
                background-color: #667eea;
                border-color: #667eea;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            }

            .form-label {
                font-weight: 600;
                color: #495057;
                margin-bottom: 8px;
                font-size: 0.95rem;
            }

            .invalid-feedback {
                font-size: 0.875rem;
                font-weight: 500;
            }

            .section-header {
                border-bottom: 2px solid rgba(102, 126, 234, 0.1);
                padding-bottom: 15px;
                margin-bottom: 25px;
            }

            .section-header h5 {
                color: #495057;
                font-weight: 600;
                margin: 0;
                display: flex;
                align-items: center;
            }

            .section-header i {
                color: #667eea;
                margin-right: 10px;
            }

            .order-summary-sticky {
                position: sticky;
                top: 20px;
                max-height: calc(100vh - 40px);
                overflow-y: auto;
            }

            .order-item {
                padding: 15px 0;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
            }

            .order-item:hover {
                background: rgba(102, 126, 234, 0.02);
                border-radius: 8px;
                margin: 0 -10px;
                padding: 15px 10px;
            }

            .order-item:last-child {
                border-bottom: none;
            }

            .product-image {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .total-section {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
                border-radius: 12px;
                padding: 20px;
                margin-top: 20px;
                border: 2px solid rgba(102, 126, 234, 0.2);
            }

            .total-amount {
                font-size: 1.5rem;
                font-weight: 700;
                color: #667eea;
            }

            .discount-badge {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
                display: inline-block;
                margin-left: 10px;
            }

            @media (max-width: 768px) {
                .checkout-container {
                    margin: 10px;
                    border-radius: 15px;
                }

                .stepper .nav-link {
                    padding: 10px 12px;
                    font-size: 0.9rem;
                }

                .step-number {
                    width: 24px;
                    height: 24px;
                    font-size: 0.8rem;
                    margin-right: 8px;
                }

                .card-header {
                    padding: 15px 20px;
                }

                .card-header h4 {
                    font-size: 1.2rem;
                }

                .order-summary-sticky {
                    position: static;
                    max-height: none;
                }
            }

            /* Loading animation */
            @keyframes pulse {
                0% {
                    opacity: 1;
                }

                50% {
                    opacity: 0.5;
                }

                100% {
                    opacity: 1;
                }
            }

            .loading {
                animation: pulse 1.5s ease-in-out infinite;
            }

            /* Smooth transitions */
            .tab-pane {
                transition: all 0.3s ease;
            }

            .fade:not(.show) {
                opacity: 0;
                transform: translateX(20px);
            }

            .fade.show {
                opacity: 1;
                transform: translateX(0);
            }
        </style>
        #order-processing-overlay {
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        z-index: 2000;
        display: flex;
        align-items: center;
        justify-content: center;
        }
        #order-processing-overlay .overlay-bg {
        position: absolute;
        top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(102, 126, 234, 0.15);
        backdrop-filter: blur(2px);
        z-index: 1;
        }
        #order-processing-overlay .overlay-content {
        position: relative;
        z-index: 2;
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
        padding: 40px 32px 32px 32px;
        display: flex;
        flex-direction: column;
        align-items: center;
        }
        @media (max-width: 991.98px) {
        .sticky-top {
        position: static !important;
        }
        .card-body, .card-header {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
        }
        }
        @media (max-width: 575.98px) {
        .card-body, .card-header {
        padding: 1rem !important;
        }
        .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1rem;
        }
        }
        </style>