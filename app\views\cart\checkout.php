<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Checkout - Premium Shopping Experience</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>

<body>

    <!-- Notification Area -->
    <div id="notification-area" aria-live="polite" class="position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999; margin-top: 20px;"></div>

    <!-- Processing Overlay -->
    <div id="processing-overlay" class="processing-overlay" style="display: none;">
        <div class="processing-backdrop"></div>
        <div class="processing-content">
            <div class="processing-animation">
                <div class="processing-circle"></div>
                <div class="processing-circle"></div>
                <div class="processing-circle"></div>
            </div>
            <h3 class="processing-title">Processing Your Order</h3>
            <p class="processing-subtitle">Please wait while we securely process your payment...</p>
            <div class="processing-steps">
                <div class="processing-step active">
                    <i class="fas fa-credit-card"></i>
                    <span>Validating Payment</span>
                </div>
                <div class="processing-step">
                    <i class="fas fa-box"></i>
                    <span>Creating Order</span>
                </div>
                <div class="processing-step">
                    <i class="fas fa-check-circle"></i>
                    <span>Confirmation</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Checkout Container -->
    <div class="checkout-container">
        <div class="container-fluid">
            <div class="row g-0 min-vh-100">

                <!-- Left Side - Checkout Form -->
                <div class="col-lg-8 checkout-form-section">
                    <div class="checkout-form-wrapper">

                        <!-- Header -->
                        <div class="checkout-header">
                            <div class="container">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h1 class="checkout-title">
                                            <i class="fas fa-shield-alt me-3"></i>
                                            Secure Checkout
                                        </h1>
                                        <p class="checkout-subtitle">Complete your purchase with confidence</p>
                                    </div>
                                    <div class="col-auto">
                                        <div class="security-indicators">
                                            <div class="security-badge">
                                                <i class="fas fa-lock"></i>
                                                <span>SSL Secured</span>
                                            </div>
                                            <div class="security-badge">
                                                <i class="fas fa-shield-check"></i>
                                                <span>256-bit Encryption</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Stepper -->
                        <div class="stepper-section">
                            <div class="container">
                                <div class="stepper-container">
                                    <div class="stepper-progress-track">
                                        <div class="stepper-progress-bar" id="progressBar"></div>
                                    </div>
                                    <div class="stepper-steps">
                                        <div class="stepper-step active" data-step="1" id="step-1">
                                            <div class="step-indicator">
                                                <div class="step-number">1</div>
                                                <div class="step-check"><i class="fas fa-check"></i></div>
                                            </div>
                                            <div class="step-info">
                                                <h6 class="step-title">Billing Information</h6>
                                                <p class="step-description">Your contact details</p>
                                            </div>
                                        </div>
                                        <div class="stepper-step" data-step="2" id="step-2">
                                            <div class="step-indicator">
                                                <div class="step-number">2</div>
                                                <div class="step-check"><i class="fas fa-check"></i></div>
                                            </div>
                                            <div class="step-info">
                                                <h6 class="step-title">Shipping Details</h6>
                                                <p class="step-description">Delivery information</p>
                                            </div>
                                        </div>
                                        <div class="stepper-step" data-step="3" id="step-3">
                                            <div class="step-indicator">
                                                <div class="step-number">3</div>
                                                <div class="step-check"><i class="fas fa-check"></i></div>
                                            </div>
                                            <div class="step-info">
                                                <h6 class="step-title">Payment Method</h6>
                                                <p class="step-description">Secure payment</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Content -->
                        <div class="form-content">
                            <div class="container">
                                <form action="<?= UrlHelper::url('/checkout') ?>" method="POST" id="checkoutForm" novalidate>
                                    <!-- Step 1: Billing -->
                                    <div class="checkout-step" data-step="1">
                                        <div class="step-title mb-4"><i class="fas fa-user me-2"></i>Billing Information</div>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="form-floating input-group">
                                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                    <input type="text" class="form-control" id="billing_first_name" name="billing_first_name" placeholder="First Name" value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                                                    <label for="billing_first_name">First Name *</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating input-group">
                                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                    <input type="text" class="form-control" id="billing_last_name" name="billing_last_name" placeholder="Last Name" value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                                                    <label for="billing_last_name">Last Name *</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row g-3 mt-2">
                                            <div class="col-md-6">
                                                <div class="form-floating input-group">
                                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                                    <input type="email" class="form-control" id="billing_email" name="billing_email" placeholder="Email" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                                    <label for="billing_email">Email Address *</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating input-group">
                                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                                    <input type="tel" class="form-control" id="billing_phone" name="billing_phone" placeholder="Phone" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                                    <label for="billing_phone">Phone Number</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-floating input-group mt-3">
                                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                            <input type="text" class="form-control" id="billing_address" name="billing_address" placeholder="Address" required>
                                            <label for="billing_address">Address *</label>
                                        </div>
                                        <div class="row g-3 mt-2">
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="billing_city" name="billing_city" placeholder="City" required>
                                                    <label for="billing_city">City *</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="billing_state" name="billing_state" placeholder="State" required>
                                                    <label for="billing_state">State/Province *</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row g-3 mt-2">
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="billing_postal_code" name="billing_postal_code" placeholder="Postal Code" required>
                                                    <label for="billing_postal_code">Postal Code *</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="billing_country" name="billing_country" placeholder="Country" required>
                                                    <label for="billing_country">Country *</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-end mt-4">
                                            <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                                        </div>
                                    </div>
                                    <!-- Step 2: Shipping -->
                                    <div class="checkout-step d-none" data-step="2">
                                        <div class="step-title mb-4"><i class="fas fa-shipping-fast me-2"></i>Shipping Information</div>
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="same_as_billing" checked>
                                            <label class="form-check-label" for="same_as_billing">Same as billing address</label>
                                        </div>
                                        <div id="shipping-fields" style="display: none;">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="shipping_first_name" name="shipping_first_name" placeholder="First Name">
                                                        <label for="shipping_first_name">First Name *</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="shipping_last_name" name="shipping_last_name" placeholder="Last Name">
                                                        <label for="shipping_last_name">Last Name *</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-floating mt-3">
                                                <input type="text" class="form-control" id="shipping_address" name="shipping_address" placeholder="Address">
                                                <label for="shipping_address">Address *</label>
                                            </div>
                                            <div class="row g-3 mt-2">
                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="shipping_city" name="shipping_city" placeholder="City">
                                                        <label for="shipping_city">City *</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="shipping_state" name="shipping_state" placeholder="State">
                                                        <label for="shipping_state">State/Province *</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row g-3 mt-2">
                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="shipping_postal_code" name="shipping_postal_code" placeholder="Postal Code">
                                                        <label for="shipping_postal_code">Postal Code *</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="shipping_country" name="shipping_country" placeholder="Country">
                                                        <label for="shipping_country">Country *</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between mt-4">
                                            <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i>Back</button>
                                            <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                                        </div>
                                    </div>
                                    <!-- Step 3: Payment -->
                                    <div class="checkout-step d-none" data-step="3">
                                        <div class="step-title mb-4"><i class="fas fa-credit-card me-2"></i>Payment Information</div>
                                        <div class="mb-3">
                                            <div class="form-floating input-group">
                                                <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                                <input type="text" class="form-control" id="card_number" name="card_number" placeholder="Card Number" required maxlength="19">
                                                <label for="card_number">Card Number *</label>
                                            </div>
                                        </div>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="form-floating input-group">
                                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                                    <input type="text" class="form-control" id="card_expiry" name="card_expiry" placeholder="MM/YY" required maxlength="5">
                                                    <label for="card_expiry">Expiry Date *</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating input-group">
                                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                                    <input type="text" class="form-control" id="card_cvv" name="card_cvv" placeholder="CVV" required maxlength="4">
                                                    <label for="card_cvv">CVV *</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-floating input-group mt-3">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="card_name" name="card_name" placeholder="Name on Card" required>
                                            <label for="card_name">Name on Card *</label>
                                        </div>
                                        <div class="d-flex justify-content-between mt-4">
                                            <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i>Back</button>
                                            <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                                        </div>
                                    </div>
                                    <!-- Step 4: Review -->
                                    <div class="checkout-step d-none" data-step="4">
                                        <div class="step-title mb-4"><i class="fas fa-clipboard-check me-2"></i>Review & Place Order</div>
                                        <div id="review-section"></div>
                                        <div class="mb-4">
                                            <div class="form-floating">
                                                <textarea class="form-control" id="order_notes" name="order_notes" placeholder="Order Notes" style="height: 80px"></textarea>
                                                <label for="order_notes">Order Notes (Optional)</label>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between mt-4">
                                            <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i>Back</button>
                                            <button type="submit" class="btn btn-primary btn-lg"> <i class="fas fa-lock me-2"></i>Place Order</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- Order Summary -->
                        <div class="col-xl-5 col-lg-4 col-12 mt-xl-0 mt-4">
                            <div class="order-summary-modern sticky-xl-top">
                                <div class="summary-card card shadow-lg">
                                    <div class="card-header summary-header">
                                        <h4 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>Order Summary</h4>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($cartItems)): ?>
                                            <?php foreach ($cartItems as $item): ?>
                                                <div class="summary-item d-flex align-items-center mb-3 p-2 rounded">
                                                    <img src="<?php echo htmlspecialchars($item['image_path'] ?? '/assets/images/placeholder.jpg'); ?>" alt="<?php echo htmlspecialchars($item['name']); ?>" class="summary-img me-3">
                                                    <div class="flex-grow-1">
                                                        <div class="fw-semibold mb-1"><?php echo htmlspecialchars($item['name']); ?></div>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="text-muted small">Qty: <?php echo $item['quantity']; ?></span>
                                                            <span class="fw-bold text-primary">$<?php echo number_format(($item['sale_price'] ?? $item['price']) * $item['quantity'], 2); ?></span>
                                                        </div>
                                                        <?php if (isset($item['sale_price']) && $item['sale_price']): ?>
                                                            <span class="badge bg-danger text-white mt-1">On Sale</span>
                                                            <span class="text-muted text-decoration-line-through small ms-2">$<?php echo number_format($item['price'] * $item['quantity'], 2); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                            <hr>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Subtotal:</span>
                                                <span>$<?php echo number_format($subtotal, 2); ?></span>
                                            </div>
                                            <?php if (isset($discount) && $discount > 0): ?>
                                                <div class="d-flex justify-content-between mb-2 text-success">
                                                    <span>Discount:</span>
                                                    <span>-$<?php echo number_format($discount, 2); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Shipping:</span>
                                                <span>$<?php echo number_format($shipping, 2); ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Tax:</span>
                                                <span>$<?php echo number_format($tax, 2); ?></span>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between align-items-center p-3 rounded total-highlight">
                                                <span class="fw-bold fs-5">Total:</span>
                                                <span class="fw-bold fs-4 text-primary">$<?php echo number_format($total, 2); ?></span>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="fas fa-shopping-cart text-muted mb-3" style="font-size: 3rem;"></i>
                                                <h6 class="text-muted">Your cart is empty</h6>
                                                <a href="/products" class="btn btn-primary">Continue Shopping</a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Order Processing Overlay -->
            <div id="order-processing-overlay" style="display:none;">
                <div class="overlay-bg"></div>
                <div class="overlay-content" role="status" aria-live="polite">
                    <div class="spinner-border text-primary mb-3" style="width:3rem;height:3rem;" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="fw-bold fs-5">Processing your order...</div>
                </div>
            </div>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8/jquery.inputmask.min.js"></script>
            <script>
                // Notification helper
                function showNotification(message, type = 'info') {
                    const area = document.getElementById('notification-area');
                    area.innerHTML = `<div class="alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show" role="alert">${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>`;
                }
                // Input masks
                $(function() {
                    $('#card_number').inputmask('9999 9999 9999 9999[ 9999 9999]');
                    $('#card_expiry').inputmask('99/99');
                    $('#billing_phone').inputmask('+9{1,3} ************');
                });
                // Toggle shipping fields
                document.getElementById('same_as_billing').addEventListener('change', function() {
                    const shippingFields = document.getElementById('shipping-fields');
                    const shippingInputs = shippingFields.querySelectorAll('input');
                    if (this.checked) {
                        shippingFields.style.display = 'none';
                        shippingInputs.forEach(input => input.removeAttribute('required'));
                        // Copy billing to shipping
                        document.getElementById('shipping_first_name').value = document.getElementById('billing_first_name').value;
                        document.getElementById('shipping_last_name').value = document.getElementById('billing_last_name').value;
                        document.getElementById('shipping_address').value = document.getElementById('billing_address').value;
                        document.getElementById('shipping_city').value = document.getElementById('billing_city').value;
                        document.getElementById('shipping_state').value = document.getElementById('billing_state').value;
                        document.getElementById('shipping_postal_code').value = document.getElementById('billing_postal_code').value;
                        document.getElementById('shipping_country').value = document.getElementById('billing_country').value;
                    } else {
                        shippingFields.style.display = 'block';
                        shippingInputs.forEach(input => input.setAttribute('required', 'required'));
                    }
                });
                // Wizard step navigation
                $(function() {
                    let currentStep = 1;
                    const totalSteps = 4;

                    function showStep(step) {
                        $('.checkout-step').addClass('d-none');
                        $(`.checkout-step[data-step="${step}"]`).removeClass('d-none');
                        $('.stepper-step').removeClass('active completed done');
                        for (let i = 1; i < step; i++) {
                            $(`.stepper-step[data-step="${i}"]`).addClass('completed done').find('.stepper-icon').html('<i class="fas fa-check"></i>');
                        }
                        $(`.stepper-step[data-step="${step}"]`).addClass('active').find('.stepper-icon').html(stepIcons[step]);
                        // Animate progress bar
                        const percent = ((step - 1) / (totalSteps - 1)) * 100;
                        $('#stepperBar').css('width', percent + '%');
                        // ARIA
                        $('.stepper-step').attr('aria-current', 'false');
                        $(`.stepper-step[data-step="${step}"]`).attr('aria-current', 'step');
                        currentStep = step;
                    }
                    // Step icons for restoring
                    const stepIcons = {
                        1: '<i class="fas fa-user"></i>',
                        2: '<i class="fas fa-shipping-fast"></i>',
                        3: '<i class="fas fa-credit-card"></i>',
                        4: '<i class="fas fa-clipboard-check"></i>'
                    };
                    // Keyboard navigation
                    $('#stepperSteps').on('keydown', '.stepper-step', function(e) {
                        if (e.key === 'Enter' || e.key === ' ') {
                            const step = parseInt($(this).data('step'));
                            if (step < currentStep) showStep(step);
                        }
                        if (e.key === 'ArrowRight') {
                            $(this).next('.stepper-step').focus();
                        }
                        if (e.key === 'ArrowLeft') {
                            $(this).prev('.stepper-step').focus();
                        }
                    });
                    // Next/Prev buttons
                    $('.next-step').on('click', function() {
                        if (currentStep === 1 && !validateBilling()) return;
                        if (currentStep === 2 && !validateShipping()) return;
                        if (currentStep === 3 && !validatePayment()) return;
                        if (currentStep < totalSteps) showStep(currentStep + 1);
                        if (currentStep === 3) populateReview();
                    });
                    $('.prev-step').on('click', function() {
                        if (currentStep > 1) showStep(currentStep - 1);
                    });
                    // Stepper click (optional, for direct navigation)
                    $('.stepper-step').on('click', function() {
                        const step = parseInt($(this).data('step'));
                        if (step < currentStep) showStep(step);
                    });
                    showStep(1);
                    // Validation functions
                    function validateBilling() {
                        let valid = true;
                        $('#billing_first_name, #billing_last_name, #billing_email, #billing_address, #billing_city, #billing_state, #billing_postal_code, #billing_country').each(function() {
                            if (!$(this).val()) {
                                $(this).addClass('is-invalid');
                                valid = false;
                            } else {
                                $(this).removeClass('is-invalid');
                            }
                        });
                        if (!valid) showNotification('Please fill all required billing fields.', 'error');
                        return valid;
                    }

                    function validateShipping() {
                        if ($('#same_as_billing').is(':checked')) return true;
                        let valid = true;
                        $('#shipping_first_name, #shipping_last_name, #shipping_address, #shipping_city, #shipping_state, #shipping_postal_code, #shipping_country').each(function() {
                            if (!$(this).val()) {
                                $(this).addClass('is-invalid');
                                valid = false;
                            } else {
                                $(this).removeClass('is-invalid');
                            }
                        });
                        if (!valid) showNotification('Please fill all required shipping fields.', 'error');
                        return valid;
                    }

                    function validatePayment() {
                        let valid = true;
                        $('#card_number, #card_expiry, #card_cvv, #card_name').each(function() {
                            if (!$(this).val()) {
                                $(this).addClass('is-invalid');
                                valid = false;
                            } else {
                                $(this).removeClass('is-invalid');
                            }
                        });
                        if (!valid) showNotification('Please fill all required payment fields.', 'error');
                        return valid;
                    }
                    // Populate review step
                    function populateReview() {
                        const review = $('#review-section');
                        review.html(`
            <div class='mb-4'>
                <h6 class='fw-bold mb-2'><i class='fas fa-user me-2'></i>Billing</h6>
                <div><b>Name:</b> ${$('#billing_first_name').val()} ${$('#billing_last_name').val()}</div>
                <div><b>Email:</b> ${$('#billing_email').val()}</div>
                <div><b>Phone:</b> ${$('#billing_phone').val()}</div>
                <div><b>Address:</b> ${$('#billing_address').val()}, ${$('#billing_city').val()}, ${$('#billing_state').val()}, ${$('#billing_postal_code').val()}, ${$('#billing_country').val()}</div>
            </div>
            <div class='mb-4'>
                <h6 class='fw-bold mb-2'><i class='fas fa-shipping-fast me-2'></i>Shipping</h6>
                <div>${$('#same_as_billing').is(':checked') ? '<span class="badge bg-success">Same as billing</span>' :
                    `<div><b>Name:</b> ${$('#shipping_first_name').val()} ${$('#shipping_last_name').val()}</div>
                    <div><b>Address:</b> ${$('#shipping_address').val()}, ${$('#shipping_city').val()}, ${$('#shipping_state').val()}, ${$('#shipping_postal_code').val()}, ${$('#shipping_country').val()}</div>`}
            </div>
            <div class='mb-4'>
                <h6 class='fw-bold mb-2'><i class='fas fa-credit-card me-2'></i>Payment</h6>
                <div><b>Name on Card:</b> ${$('#card_name').val()}</div>
                <div><b>Card:</b> **** **** **** ${$('#card_number').val().slice(-4)}</div>
                <div><b>Expiry:</b> ${$('#card_expiry').val()}</div>
            </div>
        `);
                    }
                });
                // Enhanced stepper integration functions
                function updateStepperToProcessing() {
                    // Add processing state to stepper
                    const stepper = document.querySelector('.stepper');
                    if (stepper) {
                        stepper.classList.add('processing');

                        // Add processing animation to all steps
                        const steps = stepper.querySelectorAll('.nav-link');
                        steps.forEach(step => {
                            step.classList.add('processing-step');
                        });

                        // Show processing indicator
                        const processingIndicator = document.createElement('div');
                        processingIndicator.className = 'stepper-processing';
                        processingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing Order...';
                        stepper.appendChild(processingIndicator);
                    }
                }

                function validateStepFields(stepElement) {
                    let isValid = true;
                    const requiredFields = stepElement.querySelectorAll('input[required], select[required]');

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.classList.add('is-invalid');
                            isValid = false;

                            // Add shake animation for invalid fields
                            field.classList.add('shake-error');
                            setTimeout(() => field.classList.remove('shake-error'), 600);
                        } else {
                            field.classList.remove('is-invalid');
                        }
                    });

                    return isValid;
                }

                function showStepSuccess(stepNumber) {
                    const step = document.querySelector(`[data-step="${stepNumber}"]`);
                    if (step) {
                        step.classList.add('step-success');

                        // Add success checkmark animation
                        const checkmark = document.createElement('div');
                        checkmark.className = 'step-checkmark';
                        checkmark.innerHTML = '<i class="fas fa-check"></i>';
                        step.appendChild(checkmark);

                        // Animate checkmark
                        setTimeout(() => checkmark.classList.add('animate-in'), 100);
                    }
                }

                // Form validation with stepper integration
                document.getElementById('checkoutForm').addEventListener('submit', function(e) {
                    // Update stepper to show processing state
                    updateStepperToProcessing();
                    const cardNumber = document.getElementById('card_number').value.replace(/\s/g, '');
                    const cardExpiry = document.getElementById('card_expiry').value;
                    const cardCvv = document.getElementById('card_cvv').value;
                    // Basic card validation
                    if (cardNumber.length < 13 || cardNumber.length > 19) {
                        e.preventDefault();
                        showNotification('Please enter a valid card number.', 'error');
                        return;
                    }
                    if (!/^\d{2}\/\d{2}$/.test(cardExpiry)) {
                        e.preventDefault();
                        showNotification('Please enter expiry date in MM/YY format.', 'error');
                        return;
                    }
                    if (cardCvv.length < 3 || cardCvv.length > 4) {
                        e.preventDefault();
                        showNotification('Please enter a valid CVV.', 'error');
                        return;
                    }
                    // Disable submit button to prevent double submission
                    const submitBtn = this.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.classList.add('loading');
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    // Show overlay
                    document.getElementById('order-processing-overlay').style.display = 'block';

                    // Add final step completion animation
                    setTimeout(() => {
                        showStepSuccess(3); // Payment step
                    }, 500);
                });

                // Enhanced stepper navigation with animations
                function initializeStepperEnhancements() {
                    const stepperSteps = document.querySelectorAll('.stepper-step');
                    const formSections = document.querySelectorAll('.checkout-section');

                    // Add real-time validation
                    const formInputs = document.querySelectorAll('input[required], select[required]');
                    formInputs.forEach(input => {
                        input.addEventListener('blur', function() {
                            validateSingleField(this);
                        });

                        input.addEventListener('input', function() {
                            if (this.classList.contains('is-invalid')) {
                                validateSingleField(this);
                            }
                        });
                    });

                    // Add smooth focus transitions
                    const allInputs = document.querySelectorAll('input, select, textarea');
                    allInputs.forEach(input => {
                        input.addEventListener('focus', function() {
                            this.parentElement.classList.add('input-focused');
                        });

                        input.addEventListener('blur', function() {
                            this.parentElement.classList.remove('input-focused');
                        });
                    });
                }

                function validateSingleField(field) {
                    if (field.hasAttribute('required') && !field.value.trim()) {
                        field.classList.add('is-invalid');
                        return false;
                    } else {
                        field.classList.remove('is-invalid');
                        return true;
                    }
                }

                function updateProgressBar(currentStep) {
                    const progressBar = document.querySelector('.progress-bar');
                    if (progressBar) {
                        const totalSteps = 3;
                        const progress = ((currentStep + 1) / totalSteps) * 100;
                        progressBar.style.width = progress + '%';
                    }
                }

                // Enhanced form submission feedback
                function showFormSuccess() {
                    const form = document.getElementById('checkoutForm');
                    form.classList.add('form-success');

                    // Add success animation to all completed steps
                    setTimeout(() => {
                        for (let i = 1; i <= 3; i++) {
                            showStepSuccess(i);
                        }
                    }, 200);
                }

                // Initialize enhancements when DOM is ready
                document.addEventListener('DOMContentLoaded', function() {
                    initializeStepperEnhancements();
                    updateProgressBar(0); // Start with first step

                    // Add smooth scroll behavior for better UX
                    document.documentElement.style.scrollBehavior = 'smooth';
                });
            </script>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

                body,
                .checkout-bg {
                    background: linear-gradient(135deg, #e0e7ff 0%, #f5f7fa 100%);
                    min-height: 100vh;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                }

                .checkout-bg {
                    padding: 40px 0;
                }

                .checkout-main {
                    max-width: 1200px;
                }

                .checkout-card {
                    background: #fff;
                    border-radius: 24px;
                    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.10);
                    padding: 32px 32px 24px 32px;
                    margin-bottom: 24px;
                    position: relative;
                    overflow: hidden;
                }

                .checkout-stepper {
                    position: relative;
                    margin-bottom: 32px;
                }

                .stepper-progress {
                    position: absolute;
                    top: 24px;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: #e0e7ff;
                    border-radius: 2px;
                    z-index: 0;
                }

                .stepper-bar {
                    height: 100%;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                    border-radius: 2px;
                    width: 0%;
                    transition: width 0.4s cubic-bezier(.4, 0, .2, 1);
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.10);
                }

                .stepper-steps {
                    position: relative;
                    z-index: 1;
                }

                .stepper-step {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    cursor: pointer;
                    min-width: 80px;
                    transition: color 0.2s, background 0.2s, box-shadow 0.2s;
                    outline: none;
                    position: relative;
                }

                .stepper-step:focus {
                    box-shadow: 0 0 0 3px #a5b4fc;
                    z-index: 2;
                }

                .stepper-step .stepper-icon {
                    width: 44px;
                    height: 44px;
                    border-radius: 50%;
                    background: #e0e7ff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.5rem;
                    color: #667eea;
                    margin-bottom: 6px;
                    border: 2px solid #e0e7ff;
                    transition: background 0.2s, color 0.2s, border 0.2s;
                }

                .stepper-step.active .stepper-icon {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: #fff;
                    border: 2px solid #764ba2;
                    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
                }

                .stepper-step.completed .stepper-icon,
                .stepper-step.done .stepper-icon {
                    background: #22c55e;
                    color: #fff;
                    border: 2px solid #22c55e;
                }

                .stepper-step.completed .stepper-label,
                .stepper-step.done .stepper-label {
                    color: #22c55e;
                }

                .stepper-step .stepper-label {
                    font-size: 1rem;
                    font-weight: 700;
                    color: #667eea;
                    letter-spacing: 0.01em;
                    margin-top: 2px;
                    transition: color 0.2s;
                }

                .stepper-step.active .stepper-label {
                    color: #764ba2;
                }

                .stepper-step:hover:not(.active) .stepper-icon {
                    background: #c7d2fe;
                    color: #764ba2;
                    border: 2px solid #c7d2fe;
                }

                .stepper-step:hover:not(.active) .stepper-label {
                    color: #764ba2;
                }

                .checkout-step {
                    animation: fadeInStep 0.4s cubic-bezier(.4, 0, .2, 1);
                }

                @keyframes fadeInStep {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }

                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .step-title {
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: #764ba2;
                    margin-bottom: 18px;
                    display: flex;
                    align-items: center;
                }

                .form-floating>.input-group>.form-control,
                .form-floating>.input-group>.input-group-text {
                    height: calc(3.5rem + 2px);
                    line-height: 1.25;
                }

                .form-floating>.input-group>.form-control {
                    border-radius: 0 12px 12px 0;
                    border-left: none;
                }

                .form-floating>.input-group>.input-group-text {
                    border-radius: 12px 0 0 12px;
                    border-right: none;
                    background: #e0e7ff;
                    color: #667eea;
                    font-weight: 500;
                }

                .form-floating>.form-control,
                .form-floating>textarea {
                    border-radius: 12px;
                    border: 2px solid #e0e7ff;
                    background: #f8fafc;
                    font-size: 1rem;
                    transition: border-color 0.2s;
                }

                .form-floating>.form-control:focus,
                .form-floating>textarea:focus {
                    border-color: #764ba2;
                    background: #fff;
                }

                .form-floating>label {
                    color: #667eea;
                    font-weight: 500;
                }

                .form-control.is-invalid,
                .form-floating>.form-control.is-invalid {
                    border-color: #dc3545;
                    background: #fff0f3;
                }

                .btn {
                    border-radius: 12px;
                    font-weight: 600;
                    padding: 12px 24px;
                    transition: all 0.2s;
                    border: none;
                }

                .btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: #fff;
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
                }

                .btn-primary:hover {
                    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                    color: #fff;
                    transform: translateY(-2px);
                }

                .btn-outline-secondary {
                    border: 2px solid #6c757d;
                    color: #6c757d;
                    background: transparent;
                }

                .btn-outline-secondary:hover {
                    background: #6c757d;
                    color: #fff;
                }

                .btn:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                .order-summary-modern {
                    position: relative;
                    z-index: 2;
                }

                .summary-card {
                    border-radius: 20px;
                    background: #fff;
                    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.10);
                    overflow: hidden;
                }

                .summary-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: #fff;
                    border-radius: 20px 20px 0 0;
                    padding: 20px 25px;
                }

                .summary-item {
                    background: #f8fafc;
                    border-radius: 12px;
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.04);
                    margin-bottom: 10px;
                }

                .summary-img {
                    width: 56px;
                    height: 56px;
                    object-fit: cover;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                }

                .total-highlight {
                    background: linear-gradient(135deg, #e0e7ff 0%, #f5f7fa 100%);
                    border: 2px solid #764ba2;
                    color: #764ba2;
                }

                @media (max-width: 991.98px) {
                    .checkout-main {
                        max-width: 100%;
                    }

                    .order-summary-modern {
                        position: static;
                        margin-top: 32px;
                    }

                    .sticky-xl-top {
                        position: static !important;
                    }
                }

                @media (max-width: 767.98px) {

                    .checkout-card,
                    .summary-card {
                        padding: 16px 8px;
                        border-radius: 12px;
                    }

                    .summary-header {
                        padding: 12px 12px;
                        font-size: 1.1rem;
                    }

                    .summary-img {
                        width: 44px;
                        height: 44px;
                    }
                }

                /* Enhanced stepper animations and effects */
                .stepper.processing {
                    position: relative;
                    overflow: visible;
                }

                .stepper-processing {
                    position: absolute;
                    top: 100%;
                    left: 50%;
                    transform: translateX(-50%);
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 25px;
                    font-weight: 600;
                    font-size: 0.9rem;
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                    animation: slideInUp 0.5s ease-out;
                    z-index: 1000;
                    margin-top: 10px;
                }

                .processing-step {
                    animation: processingPulse 2s ease-in-out infinite;
                }

                .step-success {
                    position: relative;
                }

                .step-checkmark {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    width: 24px;
                    height: 24px;
                    background: #28a745;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 12px;
                    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
                    transform: scale(0);
                    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                }

                .step-checkmark.animate-in {
                    transform: scale(1);
                }

                .shake-error {
                    animation: shake 0.6s ease-in-out;
                }

                /* Enhanced form field animations */
                .form-control:focus {
                    transform: translateY(-1px) scale(1.01);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .form-control.is-invalid {
                    animation: shake 0.6s ease-in-out;
                }

                /* Progress indicator enhancements */
                .progress-bar {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                }

                .progress-bar::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                    animation: shimmer 2s infinite;
                }

                /* Keyframe animations */
                @keyframes slideInUp {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(20px);
                    }

                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }

                @keyframes processingPulse {

                    0%,
                    100% {
                        opacity: 1;
                        transform: scale(1);
                    }

                    50% {
                        opacity: 0.7;
                        transform: scale(0.98);
                    }
                }

                @keyframes shake {

                    0%,
                    100% {
                        transform: translateX(0);
                    }

                    10%,
                    30%,
                    50%,
                    70%,
                    90% {
                        transform: translateX(-5px);
                    }

                    20%,
                    40%,
                    60%,
                    80% {
                        transform: translateX(5px);
                    }
                }

                @keyframes shimmer {
                    0% {
                        left: -100%;
                    }

                    100% {
                        left: 100%;
                    }
                }

                /* Step completion effects */
                .step-completed {
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
                    color: white !important;
                    transform: scale(1.05);
                    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .step-completed .step-number {
                    background: rgba(255, 255, 255, 0.3);
                }

                /* Enhanced interaction effects */
                .stepper-step:not(.active):not(.step-completed):hover {
                    transform: translateY(-2px) scale(1.02);
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                /* Loading state for buttons */
                .btn.loading {
                    position: relative;
                    color: transparent !important;
                }

                .btn.loading::after {
                    content: '';
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    top: 50%;
                    left: 50%;
                    margin-left: -10px;
                    margin-top: -10px;
                    border: 2px solid transparent;
                    border-top-color: currentColor;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    color: white;
                }

                @keyframes spin {
                    0% {
                        transform: rotate(0deg);
                    }

                    100% {
                        transform: rotate(360deg);
                    }
                }

                /* Enhanced input focus effects */
                .input-focused {
                    transform: scale(1.02);
                    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .input-focused .form-control {
                    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
                    border-color: #667eea;
                }

                .input-focused .input-group-text {
                    border-color: #667eea;
                    background: rgba(102, 126, 234, 0.15);
                    color: #667eea;
                }

                /* Form success state */
                .form-success {
                    animation: formSuccessGlow 2s ease-in-out;
                }

                @keyframes formSuccessGlow {

                    0%,
                    100% {
                        box-shadow: none;
                    }

                    50% {
                        box-shadow: 0 0 30px rgba(40, 167, 69, 0.3);
                    }
                }

                /* Fade in animation for sections */
                .fade-in {
                    animation: fadeInSlide 0.5s ease-out forwards;
                }

                @keyframes fadeInSlide {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }

                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                /* Enhanced stepper responsiveness */
                @media (max-width: 576px) {
                    .stepper-processing {
                        font-size: 0.8rem;
                        padding: 8px 16px;
                    }

                    .step-checkmark {
                        width: 20px;
                        height: 20px;
                        font-size: 10px;
                        top: -6px;
                        right: -6px;
                    }

                    .stepper-step:not(.active):not(.step-completed):hover {
                        transform: none;
                        box-shadow: none;
                    }
                }

                /* Accessibility improvements */
                .stepper-step:focus {
                    outline: 2px solid #667eea;
                    outline-offset: 2px;
                }

                .form-control:focus {
                    outline: none;
                }

                /* High contrast mode support */
                @media (prefers-contrast: high) {
                    .stepper-step.active {
                        border: 3px solid #000;
                    }

                    .step-completed {
                        border: 3px solid #28a745;
                    }
                }

                /* Reduced motion support */
                @media (prefers-reduced-motion: reduce) {

                    .stepper-step,
                    .form-control,
                    .btn,
                    .progress-bar,
                    .step-checkmark {
                        transition: none;
                        animation: none;
                    }

                    .processing-step {
                        animation: none;
                    }

                    .shake-error {
                        animation: none;
                        border-color: #dc3545 !important;
                    }
                }
            </style>