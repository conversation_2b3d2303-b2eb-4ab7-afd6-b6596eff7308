<?php

class BaseController
{
    protected $pdo;
    protected $user;
    protected $cart;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
        $this->user = $this->getCurrentUser();
        $this->cart = $this->getCart();
    }

    protected function render($view, $data = [])
    {
        try {
            // Add user and cart data to all views
            $data['user'] = $this->user;
            $data['cart'] = $this->cart;

            // Extract data to variables
            extract($data);

            // Start output buffering
            ob_start();

            // Include the view file
            $viewFile = APP_ROOT . '/app/views/' . $view . '.php';
            if (file_exists($viewFile)) {
                require_once $viewFile;
            } else {
                throw new Exception("View file not found: $viewFile");
            }

            // Get the content and clean the buffer
            $content = ob_get_clean();

            // Include the layout
            $layoutFile = APP_ROOT . '/app/views/layouts/main.php';
            if (file_exists($layoutFile)) {
                require_once $layoutFile;
            } else {
                throw new Exception("Layout file not found: $layoutFile");
            }
        } catch (Exception $e) {
            // Log the error
            error_log("View rendering error: " . $e->getMessage());

            // Show a user-friendly error page
            http_response_code(500);
            echo "<h1>Internal Server Error</h1>";
            echo "<p>Sorry, something went wrong. Please try again later.</p>";
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            exit;
        }
    }

    protected function renderPartial($view, $data = [])
    {
        extract($data);
        $viewFile = APP_ROOT . '/app/views/' . $view . '.php';
        if (file_exists($viewFile)) {
            require_once $viewFile;
        } else {
            throw new Exception("View file not found: $viewFile");
        }
    }

    protected function redirect($url)
    {
        // Remove leading slash if present to avoid double slashes
        $url = ltrim($url, '/');
        header("Location: " . APP_URL . "/" . $url);
        exit;
    }

    protected function json($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    protected function getCurrentUser()
    {
        if (isset($_SESSION['user_id'])) {
            try {
                $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
                $stmt->execute([$_SESSION['user_id']]);
                return $stmt->fetch();
            } catch (PDOException $e) {
                error_log("Database error in getCurrentUser: " . $e->getMessage());
                return null;
            }
        }
        return null;
    }

    protected function requireAuth()
    {
        if (!$this->user) {
            $this->redirect('login');
        }
    }

    protected function requireAdmin()
    {
        $this->requireAuth();
        if (!$this->user['is_admin']) {
            $this->redirect('home');
        }
    }

    protected function getCart()
    {
        $cart = [];

        if ($this->user) {
            // Get cart items for logged-in user
            try {
                $stmt = $this->pdo->prepare("
                    SELECT ci.*, p.name, p.price, p.sale_price, p.slug, c.name as category_name, pi.image_path
                    FROM cart_items ci 
                    JOIN products p ON ci.product_id = p.id 
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                    WHERE ci.user_id = ?
                ");
                $stmt->execute([$this->user['id']]);
                $cart = $stmt->fetchAll();
            } catch (PDOException $e) {
                error_log("Database error in getCart: " . $e->getMessage());
                $cart = [];
            }
        } else {
            // Get cart items from session
            if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
                $cartIds = array_keys($_SESSION['cart']);

                if (!empty($cartIds)) {
                    try {
                        $placeholders = str_repeat('?,', count($cartIds) - 1) . '?';
                        $stmt = $this->pdo->prepare("
                            SELECT p.*, c.name as category_name, pi.image_path
                            FROM products p
                            LEFT JOIN categories c ON p.category_id = c.id
                            LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                            WHERE p.id IN ($placeholders)
                        ");
                        $stmt->execute($cartIds);
                        $products = $stmt->fetchAll();

                        foreach ($products as $product) {
                            $product['quantity'] = $_SESSION['cart'][$product['id']];
                            $cart[] = $product;
                        }
                    } catch (PDOException $e) {
                        error_log("Database error in getCart session: " . $e->getMessage());
                        $cart = [];
                    }
                }
            }
        }

        return $cart;
    }

    protected function generateCSRFToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    protected function validateCSRFToken($token)
    {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    protected function sanitizeInput($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    protected function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }

    protected function uploadFile($file, $destination, $allowedTypes = null)
    {
        if ($allowedTypes === null) {
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        }

        if (!isset($file['error']) || is_array($file['error'])) {
            throw new Exception('Invalid file parameter');
        }

        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload failed');
        }

        if ($file['size'] > 5 * 1024 * 1024) { // 5MB
            throw new Exception('File too large');
        }

        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $allowedTypes)) {
            throw new Exception('File type not allowed');
        }

        $fileName = uniqid() . '.' . $fileExtension;
        $uploadPath = APP_ROOT . '/public/uploads/' . $destination . '/' . $fileName;

        // Create directory if it doesn't exist
        $uploadDir = dirname($uploadPath);
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            throw new Exception('Failed to move uploaded file');
        }

        return $fileName;
    }

    protected function sendEmail($to, $subject, $body, $altBody = null)
    {
        require_once ROOT_PATH . '/vendor/autoload.php';
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
        try {
            $mail->isSMTP();
            $mail->Host = SMTP_HOST;
            $mail->SMTPAuth = true;
            $mail->Username = SMTP_USERNAME;
            $mail->Password = SMTP_PASSWORD;
            $mail->SMTPSecure = SMTP_ENCRYPTION;
            $mail->Port = SMTP_PORT;
            $mail->setFrom(SMTP_USERNAME, APP_NAME);
            $mail->addAddress($to);
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;
            $mail->AltBody = $altBody ?: strip_tags($body);
            $mail->send();
            return true;
        } catch (\Exception $e) {
            error_log('Mailer Error: ' . $mail->ErrorInfo);
            return false;
        }
    }

    protected function sendTemplateEmail($to, $templateName, $variables = [])
    {
        // Fetch template from DB
        $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE name = ? AND is_active = 1 LIMIT 1");
        $stmt->execute([$templateName]);
        $template = $stmt->fetch();
        if (!$template) {
            error_log("Email template '$templateName' not found.");
            return false;
        }
        $subject = $template['subject'];
        $body = $template['body'];
        // Replace variables in subject and body
        foreach ($variables as $key => $value) {
            $subject = str_replace('{' . $key . '}', $value, $subject);
            $body = str_replace('{' . $key . '}', $value, $body);
        }
        return $this->sendEmail($to, $subject, $body);
    }

    protected function formatPrice($price)
    {
        return '$' . number_format($price, 2);
    }

    protected function calculateCartTotal()
    {
        $total = 0;
        foreach ($this->cart as $item) {
            $price = $item['sale_price'] ?? $item['price'];
            $total += $price * $item['quantity'];
        }
        return $total;
    }

    protected function generateSlug($text)
    {
        // Convert to lowercase
        $text = strtolower($text);

        // Replace spaces and special characters with hyphens
        $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
        $text = preg_replace('/[\s-]+/', '-', $text);

        // Remove leading and trailing hyphens
        $text = trim($text, '-');

        // If empty, use a default
        if (empty($text)) {
            $text = 'product-' . uniqid();
        }

        return $text;
    }
}
