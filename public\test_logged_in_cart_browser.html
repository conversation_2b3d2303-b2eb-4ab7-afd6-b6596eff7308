<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logged-in Cart Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .step { 
            margin: 25px 0; 
            padding: 20px; 
            border: 2px solid #e0e0e0; 
            border-radius: 10px; 
            background: #fafafa;
        }
        .step h3 {
            margin-top: 0;
            color: #555;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        button { 
            padding: 12px 20px; 
            margin: 8px; 
            cursor: pointer; 
            border: none; 
            border-radius: 6px; 
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; transform: translateY(-2px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #218838; transform: translateY(-2px); }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; transform: translateY(-2px); }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; transform: translateY(-2px); }
        
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 8px; 
            font-family: 'Courier New', monospace; 
            font-size: 13px;
            line-height: 1.4;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 2px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 2px solid #f5c6cb; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 2px solid #bee5eb; 
        }
        .status {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Logged-in Cart Test</h1>
        
        <div class="step">
            <h3><span class="step-number">1</span>Login Status</h3>
            <button onclick="checkStatus()" class="btn-info">Check Current Status</button>
            <div id="status-result" class="result info">Click to check current login and session status</div>
        </div>
        
        <div class="step">
            <h3><span class="step-number">2</span>Simulate Login</h3>
            <button onclick="simulateLogin()" class="btn-success">Login as User ID 1</button>
            <button onclick="simulateLogout()" class="btn-danger">Logout</button>
            <div id="login-result" class="result info">Click login to simulate being logged in as user ID 1</div>
        </div>
        
        <div class="step">
            <h3><span class="step-number">3</span>Test Cart Operations</h3>
            <button onclick="addItem()" class="btn-primary">Add Product 1 (Qty: 2)</button>
            <button onclick="updateItem()" class="btn-primary">Update to Qty: 5</button>
            <button onclick="removeItem()" class="btn-danger">Remove Product 1</button>
            <div id="cart-result" class="result info">Test cart operations as logged-in user</div>
        </div>
        
        <div class="step">
            <h3><span class="step-number">4</span>Check Database</h3>
            <button onclick="checkDatabase()" class="btn-info">Check cart_items Table</button>
            <div id="db-result" class="result info">Check what's actually in the database</div>
        </div>
        
        <div class="step">
            <h3><span class="step-number">5</span>View Cart Page</h3>
            <button onclick="window.open('/asma/public/cart', '_blank')" class="btn-info">Open Cart Page</button>
            <p style="margin: 10px 0; color: #666;">
                Open the cart page to see if items appear correctly for logged-in users
            </p>
        </div>
        
        <div class="step">
            <h3>🔍 Debug Log</h3>
            <div id="debug-log" class="result info">Debug information will appear here</div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function logDebug(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            updateDebugDisplay();
        }
        
        function updateDebugDisplay() {
            const debugDiv = document.getElementById('debug-log');
            debugDiv.innerHTML = debugLog.slice(-10).join('<br>'); // Show last 10 entries
        }
        
        async function checkStatus() {
            try {
                logDebug('🔄 Checking status...');
                
                const response = await fetch('/asma/public/test_logged_in_cart.php?action=status');
                const data = await response.json();
                
                const resultDiv = document.getElementById('status-result');
                resultDiv.className = 'result status';
                resultDiv.innerHTML = `
                    <strong>Session ID:</strong> ${data.session_id}<br>
                    <strong>User ID:</strong> ${data.session_user_id || 'Not logged in'}<br>
                    <strong>Is Logged In:</strong> ${data.is_logged_in ? '✅ Yes' : '❌ No'}
                `;
                
                logDebug(`📊 Status: ${data.is_logged_in ? 'Logged in' : 'Not logged in'}`);
            } catch (error) {
                logDebug(`💥 Status error: ${error.message}`);
                document.getElementById('status-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('status-result').className = 'result error';
            }
        }
        
        async function simulateLogin() {
            try {
                logDebug('🔄 Simulating login...');
                
                const response = await fetch('/asma/public/test_logged_in_cart.php?action=login');
                const data = await response.json();
                
                const resultDiv = document.getElementById('login-result');
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ ${data.message}<br><strong>User ID:</strong> ${data.session_user_id}`;
                    logDebug('✅ Login successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Login failed`;
                    logDebug('❌ Login failed');
                }
            } catch (error) {
                logDebug(`💥 Login error: ${error.message}`);
                document.getElementById('login-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('login-result').className = 'result error';
            }
        }
        
        async function simulateLogout() {
            try {
                logDebug('🔄 Logging out...');
                
                const response = await fetch('/asma/public/test_logged_in_cart.php?action=logout');
                const data = await response.json();
                
                const resultDiv = document.getElementById('login-result');
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ ${data.message}`;
                    logDebug('✅ Logout successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Logout failed`;
                    logDebug('❌ Logout failed');
                }
            } catch (error) {
                logDebug(`💥 Logout error: ${error.message}`);
                document.getElementById('login-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('login-result').className = 'result error';
            }
        }
        
        async function addItem() {
            try {
                logDebug('🔄 Adding item to cart...');
                
                const response = await fetch('/asma/public/test_logged_in_cart.php?action=add_item');
                const data = await response.json();
                
                const resultDiv = document.getElementById('cart-result');
                if (data.response && data.response.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ ${data.response.message}<br><strong>Cart Count:</strong> ${data.response.cartCount}`;
                    logDebug('✅ Add item successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ ${data.response ? data.response.message : 'Add failed'}`;
                    logDebug('❌ Add item failed');
                }
            } catch (error) {
                logDebug(`💥 Add item error: ${error.message}`);
                document.getElementById('cart-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('cart-result').className = 'result error';
            }
        }
        
        async function updateItem() {
            try {
                logDebug('🔄 Updating item in cart...');
                
                const response = await fetch('/asma/public/test_logged_in_cart.php?action=update_item');
                const data = await response.json();
                
                const resultDiv = document.getElementById('cart-result');
                if (data.response && data.response.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ ${data.response.message}<br><strong>Cart Count:</strong> ${data.response.cartCount}`;
                    logDebug('✅ Update item successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ ${data.response ? data.response.message : 'Update failed'}`;
                    logDebug('❌ Update item failed');
                }
            } catch (error) {
                logDebug(`💥 Update item error: ${error.message}`);
                document.getElementById('cart-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('cart-result').className = 'result error';
            }
        }
        
        async function removeItem() {
            try {
                logDebug('🔄 Removing item from cart...');
                
                const response = await fetch('/asma/public/test_logged_in_cart.php?action=remove_item');
                const data = await response.json();
                
                const resultDiv = document.getElementById('cart-result');
                if (data.response && data.response.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ ${data.response.message}<br><strong>Cart Count:</strong> ${data.response.cartCount}`;
                    logDebug('✅ Remove item successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ ${data.response ? data.response.message : 'Remove failed'}`;
                    logDebug('❌ Remove item failed');
                }
            } catch (error) {
                logDebug(`💥 Remove item error: ${error.message}`);
                document.getElementById('cart-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('cart-result').className = 'result error';
            }
        }
        
        async function checkDatabase() {
            try {
                logDebug('🔄 Checking database...');
                
                const response = await fetch('/asma/public/test_logged_in_cart.php?action=check_db');
                const data = await response.json();
                
                const resultDiv = document.getElementById('db-result');
                resultDiv.className = 'result info';
                resultDiv.innerHTML = `
                    <strong>Database cart_items for user 1:</strong><br>
                    ${data.cart_items.length > 0 ? 
                        data.cart_items.map(item => 
                            `ID: ${item.id}, Product: ${item.product_id}, Qty: ${item.quantity}, Created: ${item.created_at}`
                        ).join('<br>') : 
                        'No items found'
                    }
                `;
                
                logDebug(`📊 Database items: ${data.cart_items.length}`);
            } catch (error) {
                logDebug(`💥 Database check error: ${error.message}`);
                document.getElementById('db-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('db-result').className = 'result error';
            }
        }
        
        // Initialize
        logDebug('🚀 Logged-in cart test page loaded');
        checkStatus();
    </script>
</body>
</html>
