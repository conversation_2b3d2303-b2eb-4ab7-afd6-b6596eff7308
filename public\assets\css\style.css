/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-purple: #7E57C2;
    --primary-purple-dark: #5E35B1;
    --primary-purple-light: #B39DDB;
    --white: #FFFFFF;
    --dark-bg: #1a1a1a;
    --dark-card: #2d2d2d;
    --dark-surface: #3d3d3d;
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --border-color: #404040;
    --success-color: #4CAF50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196F3;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Light Mode Variables */
[data-theme="light"] {
    --dark-bg: #ffffff;
    --dark-card: #f8f9fa;
    --dark-surface: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #dee2e6;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--dark-bg);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Header */
.header {
    background-color: var(--dark-card);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
}

.logo a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-purple);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.logo a:hover {
    color: var(--primary-purple-dark);
}

.logo i {
    font-size: var(--font-size-2xl);
}

/* Navigation */
.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-purple);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-purple);
    transition: width var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--dark-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color var(--transition-fast);
}

.dropdown-menu a:hover {
    background-color: var(--dark-surface);
    color: var(--primary-purple);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Search */
.search-container {
    position: relative;
}

.search-form {
    display: flex;
    align-items: center;
}

.search-input {
    background-color: var(--dark-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    padding-right: 40px;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    width: 250px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
}

.search-btn {
    position: absolute;
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    transition: color var(--transition-fast);
}

.search-btn:hover {
    color: var(--primary-purple);
}

/* Theme Toggle */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    color: var(--primary-purple);
    background-color: var(--dark-surface);
}

/* Header Icons */
.header-icon {
    position: relative;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    text-decoration: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.header-icon:hover {
    color: var(--primary-purple);
    background-color: var(--dark-surface);
}

.badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--primary-purple);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* User Menu */
.user-menu {
    position: relative;
}

.user-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.user-toggle:hover {
    background-color: var(--dark-surface);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--dark-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
}

.user-menu:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown li {
    list-style: none;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color var(--transition-fast);
}

.user-dropdown a:hover {
    background-color: var(--dark-surface);
    color: var(--primary-purple);
}

/* Auth Buttons */
.auth-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-purple);
    color: var(--white);
}

[data-theme="light"] .btn-primary {
    color: var(--text-primary);
}

.btn-primary:hover {
    background-color: var(--primary-purple-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background-color: var(--dark-surface);
    border-color: var(--primary-purple);
    color: var(--primary-purple);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all var(--transition-fast);
}

.mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--dark-bg);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.mobile-menu.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    padding: var(--spacing-xl);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.mobile-nav ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.mobile-nav a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: var(--font-size-xl);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.mobile-nav a:hover {
    color: var(--primary-purple);
}

/* Main Content */
.main {
    min-height: calc(100vh - 200px);
    /* padding: var(--spacing-xl) 0; */
}

/* Alerts */
.alert {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.alert-error {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(244, 67, 54, 0.2);
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: 0;
    margin-left: var(--spacing-md);
}

/* Cards */
.card {
    background-color: var(--dark-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-fast);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    margin-bottom: var(--spacing-md);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.card-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Product Cards */
.product-card {
    background-color: var(--dark-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-fast);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-purple);
}

.product-image {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    background-color: var(--primary-purple);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.product-content {
    padding: var(--spacing-md);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-category {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-xs);
}

.product-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.product-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.product-price-current {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-purple);
}

.product-price-original {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    text-decoration: line-through;
}

.product-actions {
    margin-top: auto;
    display: flex;
    gap: var(--spacing-sm);
}

/* Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    background-color: var(--dark-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

/* Grid */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-cols-1 {
    grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
}

.grid-cols-5 {
    grid-template-columns: repeat(5, 1fr);
}

.grid-cols-6 {
    grid-template-columns: repeat(6, 1fr);
}

/* Footer */
.footer {
    background-color: var(--dark-card);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    margin-top: auto;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul a:hover {
    color: var(--primary-purple);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--dark-surface);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.social-links a:hover {
    background-color: var(--primary-purple);
    color: var(--white);
    transform: translateY(-2px);
}

.newsletter-form {
    display: flex;
    gap: var(--spacing-sm);
}

.newsletter-form input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--dark-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--primary-purple);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.payment-methods {
    display: flex;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    color: var(--text-muted);
}

/* Loading */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    color: var(--primary-purple);
    font-size: var(--font-size-3xl);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .grid-cols-4 {
        grid-template-columns: repeat(3, 1fr);
    }

    .grid-cols-5 {
        grid-template-columns: repeat(4, 1fr);
    }

    .grid-cols-6 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .search-input {
        width: 200px;
    }

    .grid-cols-3,
    .grid-cols-4,
    .grid-cols-5,
    .grid-cols-6 {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .newsletter-form {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .header-content {
        padding: var(--spacing-xs) 0;
    }

    .search-input {
        width: 150px;
    }

    .auth-buttons {
        display: none;
    }

    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4,
    .grid-cols-5,
    .grid-cols-6 {
        grid-template-columns: 1fr;
    }

    .product-actions {
        flex-direction: column;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-muted {
    color: var(--text-muted);
}

.text-purple {
    color: var(--primary-purple);
}

.bg-dark {
    background-color: var(--dark-bg);
}

.bg-card {
    background-color: var(--dark-card);
}

.bg-surface {
    background-color: var(--dark-surface);
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: var(--spacing-xs);
}

.mb-2 {
    margin-bottom: var(--spacing-sm);
}

.mb-3 {
    margin-bottom: var(--spacing-md);
}

.mb-4 {
    margin-bottom: var(--spacing-lg);
}

.mb-5 {
    margin-bottom: var(--spacing-xl);
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: var(--spacing-xs);
}

.mt-2 {
    margin-top: var(--spacing-sm);
}

.mt-3 {
    margin-top: var(--spacing-md);
}

.mt-4 {
    margin-top: var(--spacing-lg);
}

.mt-5 {
    margin-top: var(--spacing-xl);
}

.p-0 {
    padding: 0;
}

.p-1 {
    padding: var(--spacing-xs);
}

.p-2 {
    padding: var(--spacing-sm);
}

.p-3 {
    padding: var(--spacing-md);
}

.p-4 {
    padding: var(--spacing-lg);
}

.p-5 {
    padding: var(--spacing-xl);
}

.rounded {
    border-radius: var(--radius-md);
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.rounded-full {
    border-radius: 9999px;
}

.shadow {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.shadow-xl {
    box-shadow: var(--shadow-xl);
}

.hidden {
    display: none;
}

.block {
    display: block;
}

.inline {
    display: inline;
}

.inline-block {
    display: inline-block;
}

.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.w-full {
    width: 100%;
}

.h-full {
    height: 100%;
}

.min-h-screen {
    min-height: 100vh;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.sticky {
    position: sticky;
}

.z-10 {
    z-index: 10;
}

.z-20 {
    z-index: 20;
}

.z-30 {
    z-index: 30;
}

.z-40 {
    z-index: 40;
}

.z-50 {
    z-index: 50;
}

[data-theme="light"] .contact-send-btn {
    color: #212529 !important;
}

[data-theme="light"] .contact-send-btn:hover {
    color: #fff !important;
}

/* Discount Code Styles */
.discount-item {
    background: #d4edda !important;
    border: 1px solid #c3e6cb !important;
    border-radius: 6px !important;
    padding: 10px !important;
    margin: 5px 0 !important;
}

.discount-amount {
    color: #28a745 !important;
    font-weight: bold !important;
}

.remove-discount-btn {
    background: none !important;
    border: none !important;
    color: #dc3545 !important;
    cursor: pointer !important;
    margin-left: 10px !important;
    padding: 2px 5px !important;
    border-radius: 3px !important;
    font-size: 12px !important;
    transition: all 0.2s ease !important;
}

.remove-discount-btn:hover {
    background: #dc3545 !important;
    color: white !important;
}

.discount-message {
    margin-top: 10px !important;
    padding: 10px !important;
    border-radius: 4px !important;
    font-size: 14px !important;
}

.discount-message.success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.discount-message.error {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

.discount-form {
    margin: 15px 0 !important;
}

.discount-input-group {
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

.discount-input {
    flex: 1 !important;
    padding: 10px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-size: 14px !important;
}

.discount-input:focus {
    outline: none !important;
    border-color: #007bff !important;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3) !important;
}

.discount-form button[type="submit"]:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    background: #6c757d !important;
}

.discount-form button[type="submit"]:disabled:hover {
    background: #6c757d !important;
    transform: none !important;
}

.discount-form .fa-spinner {
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}